import { useState, useCallback, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/lib/store/authStore';
import { useRouter, useSearchParams } from 'next/navigation';
import { setCookie, deleteCookie } from 'cookies-next';
import fetchAuth, {
  LoginCredentials,
  RegisterRequest,
  LoginResponse,
  RegisterResponse,
  VerifyOtpRequest,
  RequestOtpRequest,
  OtpResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  RenewPasswordRequest,
  RenewPasswordResponse,
} from '@/lib/api/services/fetchAuth';
import { toast } from 'sonner';
import { getAuthCookieConfig } from '@/lib/utils/cookieConfig';

export interface OtpError {
  message: string;
}

// Safe hook to get search params that works with SSR
function useSafeSearchParams() {
  try {
    return useSearchParams();
  } catch {
    return null;
  }
}

// Login hook
export function useLogin() {
  const queryClient = useQueryClient();
  const { setToken } = useAuthStore();
  const [error, setError] = useState<string | null>(null);
  const [needsOtpVerification, setNeedsOtpVerification] = useState(false);
  const [verifyKey, setVerifyKey] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSafeSearchParams();

  const { mutate: login, isPending: isLoading } = useMutation({
    mutationFn: async (credentials: LoginCredentials): Promise<LoginResponse> => {
      const response = await fetchAuth.login(credentials);
      if (!response.status) {
        throw response; // Throw the LoginResponse object directly
      }
      return response;
    },
    onSuccess: (response: LoginResponse, variables: LoginCredentials) => {
      // Check if response indicates OTP verification is needed
      if (!response.status && response.message?.includes('Mã OTP đã được gửi')) {
        setNeedsOtpVerification(true);
        setVerifyKey(variables.keyLogin);
        setError(null);
        return;
      }

      // Check if login was successful
      if (response.status && response.data?.accessToken) {
        const token = response.data.accessToken;
        setToken(token);

        setCookie('auth-token', token, getAuthCookieConfig());

        // Invalidate all auth-related queries and user data
        queryClient.invalidateQueries({ queryKey: ['auth', 'login'] });
        queryClient.invalidateQueries({ queryKey: ['users', 'profile'] });
        queryClient.invalidateQueries({ queryKey: ['favorite-properties'] });
        setError(null);
        setNeedsOtpVerification(false);
        setVerifyKey(null);
        toast.success(response.message || 'Đăng nhập thành công');

        // Handle redirect parameter
        const redirectTo = searchParams?.get('redirect');
        if (redirectTo) {
          router.push(redirectTo);
        } else {
          router.refresh();
        }
        return;
      }

      setError(response.message || 'Login was unsuccessful');
      toast.error(response.message || 'Login was unsuccessful');
    },
    onError: (error: LoginResponse, variables: LoginCredentials) => {
      // Handle OTP verification needed
      if (
        error.message?.includes('Mã OTP đã được gửi') ||
        error.message?.includes('OTP đã được gửi') ||
        error.message?.includes('chưa được xác thực')
      ) {
        setNeedsOtpVerification(true);
        setVerifyKey(variables.keyLogin);
        setError(null);
        return;
      }

      setError(error.message || 'Đăng nhập thất bại');
      toast.error(error.message || 'Đăng nhập thất bại');
    },
  });

  return {
    login,
    isLoading,
    error,
    needsOtpVerification,
    verifyKey,
    clearError: () => setError(null),
    clearOtpState: () => {
      setNeedsOtpVerification(false);
      setVerifyKey(null);
    },
  };
}

// Register hook
export function useRegister() {
  // const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [keyVariable, setKeyVariable] = useState<string | null>(null);

  const {
    mutate: register,
    isPending: isLoading,
    error: registerError,
  } = useMutation({
    mutationFn: (data: RegisterRequest) => fetchAuth.register(data),
    onSuccess: (response: RegisterResponse, variables: RegisterRequest) => {
      if (response.status) {
        setSuccess(true);
        setKeyVariable(variables.keyRegister);
        toast.success(response.message);

        // Redirect to OTP page with email parameter
        // router.push(`/otp?keyRegister=${encodeURIComponent(variables.keyRegister)}`);
      } else {
        setError(response.message);
        toast.error(response.message);
      }
    },
    onError: (error: Error) => {
      const errorMessage = error.message || 'Registration failed';
      setError(errorMessage);
      toast.error(errorMessage);
    },
  });

  return {
    register,
    isLoading,
    error: error || registerError?.message || null,
    clearError: () => setError(null),
    registerSuccess: success,
    keyVariable,
    resetRegisterState: () => {
      setSuccess(false);
      setKeyVariable(null);
    },
  };
}

// Google Login hook
export function useGoogleLogin() {
  const router = useRouter();
  const searchParams = useSafeSearchParams();
  const queryClient = useQueryClient();
  const { setToken } = useAuthStore();
  const [error, setError] = useState<string | null>(null);
  const [googleLoginSuccess, setGoogleLoginSuccess] = useState(false);

  const {
    mutate: googleLogin,
    isPending: isLoading,
    error: googleLoginError,
  } = useMutation({
    mutationFn: async (idToken: string) => {
      try {
        const response = await fetchAuth.googleLogin(idToken);
        router.refresh();
        if (!response || !response.status) {
          throw new Error(response?.message || 'Google login failed. Please try again.');
        }

        return response;
      } catch (err) {
        console.error('Google login API Error:', err);
        throw new Error(err instanceof Error ? err.message : 'Google login failed');
      }
    },
    onSuccess: (response: LoginResponse) => {
      if (response.status) {
        const token = response.data.accessToken;
        setToken(token);
        setCookie('auth-token', token, getAuthCookieConfig());
        // Invalidate all auth-related queries and user data
        queryClient.invalidateQueries({ queryKey: ['auth', 'googleLogin'] });
        queryClient.invalidateQueries({ queryKey: ['users', 'profile'] });
        queryClient.invalidateQueries({ queryKey: ['favorite-properties'] });
        setGoogleLoginSuccess(true);

        // Handle redirect parameter
        const redirectTo = searchParams?.get('redirect');
        if (redirectTo) {
          router.push(redirectTo);
        } else {
          router.refresh();
        }
      } else {
        setError(response.message || 'Google login failed');
      }
    },
    onError: (error: Error) => {
      console.error('Google login failed:', error);
      setError(error.message || 'Google login failed');
    },
  });

  return {
    googleLogin,
    isLoading,
    error: error || googleLoginError?.message || null,
    googleLoginSuccess,
    clearError: () => setError(null),
  };
}

// Zalo Login hook with redirect flow
export function useZaloLogin() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  const { setToken } = useAuthStore();
  const [error, setError] = useState<string | null>(null);
  const [zaloLoginSuccess, setZaloLoginSuccess] = useState(false);

  // Check if we're returning from Zalo callback
  useEffect(() => {
    const code = searchParams?.get('code');
    const state = searchParams?.get('state');
    const error = searchParams?.get('error');

    if (error) {
      setError('Zalo authentication failed: ' + error);
      return;
    }

    if (code && state) {
      // Handle Zalo callback
      handleZaloCallback(code, state);
    }
  }, [searchParams]);

  const handleZaloCallback = async (code: string, state: string) => {
    try {
      const response = await fetchAuth.zaloCallback(code, state);

      if (response.status && response.data?.accessToken) {
        const token = response.data.accessToken;
        setToken(token);
        setCookie('auth-token', token, getAuthCookieConfig());
        queryClient.invalidateQueries({ queryKey: ['auth', 'zaloLogin'] });
        setZaloLoginSuccess(true);
        toast.success('Đăng nhập Zalo thành công');

        // Handle redirect parameter
        const redirectTo = searchParams?.get('redirect');
        if (redirectTo) {
          router.push(redirectTo);
        } else {
          router.refresh();
        }
      } else {
        setError(response.message || 'Zalo login failed');
      }
    } catch (error) {
      console.error('Zalo callback error:', error);
      setError('Zalo authentication failed');
    }
  };

  const handleZaloLogin = useCallback(async () => {
    try {
      // Get Zalo login URL from backend
      const response = await fetchAuth.getZaloLoginUrl();

      if (!response || !response.loginUrl) {
        throw new Error('Invalid response from server: missing login URL');
      }

      // Redirect to Zalo login
      window.location.href = response.loginUrl;
    } catch (error) {
      console.error('Zalo login error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Tính năng đang được phát triển';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, []);

  return {
    zaloLogin: handleZaloLogin,
    isLoading: false,
    error,
    zaloLoginSuccess,
    clearError: () => setError(null),
  };
}

// Verify OTP hook for login flow (no auto redirect)
export function useVerifyOtpForLogin() {
  const queryClient = useQueryClient();
  const [error, setError] = useState<string | null>(null);

  const { mutate: verifyOtp, isPending: isLoading } = useMutation({
    mutationFn: async (data: VerifyOtpRequest): Promise<OtpResponse> => {
      try {
        const response = await fetchAuth.verifyOtp(data);

        if (!response || !response.status) {
          throw new Error(response?.message || 'OTP verification failed. Please try again.');
        }

        return response;
      } catch (err) {
        console.error('OTP verification API Error:', err);
        throw new Error(err instanceof Error ? err.message : 'OTP verification failed.');
      }
    },
    onSuccess: (response: OtpResponse) => {
      try {
        queryClient.invalidateQueries({ queryKey: ['auth', 'verifyOtp'] });
        setError(null);
        toast.success(response.message);
        return response;
      } catch (err) {
        console.error('Error after OTP verification:', err);
        setError('Verification successful but there was an error.');
      }
    },
    onError: (error: Error) => {
      console.error('OTP verification failed:', error);
      setError(error.message || 'OTP verification failed.');
      toast.error(error.message || 'OTP verification failed. Please try again.');
    },
  });

  return {
    verifyOtp,
    isLoading,
    error,
    clearError: () => setError(null),
  };
}

// Verify OTP hook for register flow (with auto redirect)
export function useVerifyOtp() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [error, setError] = useState<string | null>(null);

  const { mutate: verifyOtp, isPending: isLoading } = useMutation({
    mutationFn: async (data: VerifyOtpRequest): Promise<OtpResponse> => {
      try {
        const response = await fetchAuth.verifyOtp(data);

        if (!response || !response.status) {
          throw new Error(response?.message || 'OTP verification failed. Please try again.');
        }

        return response;
      } catch (err) {
        console.error('OTP verification API Error:', err);
        throw new Error(err instanceof Error ? err.message : 'OTP verification failed.');
      }
    },
    onSuccess: (response: OtpResponse) => {
      try {
        queryClient.invalidateQueries({ queryKey: ['auth', 'verifyOtp'] });
        setError(null);

        toast.success(response.message);
        router.push('/login');
        return response;
      } catch (err) {
        console.error('Error after OTP verification:', err);
        setError('Verification successful but there was an error redirecting.');
      }
    },
    onError: (error: Error) => {
      console.error('OTP verification failed:', error);
      setError(error.message || 'OTP verification failed.');
      toast.error(error.message || 'OTP verification failed. Please try again.');
    },
  });

  return {
    verifyOtp,
    isLoading,
    error,
    clearError: () => setError(null),
  };
}

// Request OTP hook
export function useRequestOtp() {
  const [error, setError] = useState<string | null>(null);

  const { mutate: requestOtp, isPending: isLoading } = useMutation({
    mutationFn: async (data: RequestOtpRequest): Promise<OtpResponse> => {
      try {
        const response = await fetchAuth.requestOtp(data);

        if (!response || !response.status) {
          throw new Error(response?.message || 'Failed to send OTP. Please try again.');
        }

        return response;
      } catch (err) {
        console.error('Request OTP API Error:', err);
        throw new Error(
          err instanceof Error
            ? err.message
            : 'Failed to send OTP. Please try again in a few minutes.'
        );
      }
    },
    onSuccess: (response: OtpResponse) => {
      setError(null);
      toast.success(response.message);
      return response;
    },
    onError: (error: Error) => {
      console.error('Request OTP failed:', error);
      setError(error.message || 'Failed to send OTP.');
      toast.error(error.message || 'Failed to send OTP. Please try again.');
    },
  });

  return {
    requestOtp,
    isLoading,
    error,
    clearError: () => setError(null),
  };
}

// Forgot Password hook
export function useForgotPassword() {
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const { mutate: forgotPassword, isPending: isLoading } = useMutation({
    mutationFn: async (data: ForgotPasswordRequest): Promise<ForgotPasswordResponse> => {
      const response = await fetchAuth.requestForgotPassword(data);
      return response;
    },
    onSuccess: response => {
      if (!response.status) {
        setError(response.message || 'Failed to send reset email.');
        toast.error(response.message || 'Failed to send reset email.');
        return;
      }
      // Reset error state and show success message
      setError(null);
      toast.success(response.message || 'Password reset email sent!');
      router.push('/login');
      return response;
    },
    onError: (error: Error) => {
      setError(error.message);
      toast.error(error.message);
    },
  });

  return {
    forgotPassword,
    isLoading,
    error,
    clearError: () => setError(null),
  };
}

// renewPassword hook
export function useRenewPassword() {
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const { mutate: renewPassword, isPending: isLoading } = useMutation({
    mutationFn: async (data: RenewPasswordRequest): Promise<RenewPasswordResponse> => {
      const response = await fetchAuth.renewPassword(data);
      return response;
    },
    onSuccess: (response: RenewPasswordResponse) => {
      if (!response.status) {
        setError(response.message || 'Đổi mật khẩu thất bại.');
        toast.error(response.message || 'Đổi mật khẩu thất bại.');
        return;
      }
      setError(null);
      toast.success(response.message || 'Đổi mật khẩu thành công!');
      router.push('/login');
      return response;
    },
    onError: (error: Error) => {
      setError(error.message);
      toast.error(error.message);
    },
  });

  return {
    renewPassword,
    isLoading,
    error,
    clearError: () => setError(null),
  };
}

// Facebook Login hook
export function useFacebookLogin() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  const { setToken } = useAuthStore();
  const [error, setError] = useState<string | null>(null);
  const [facebookLoginSuccess, setFacebookLoginSuccess] = useState(false);

  const {
    mutate: facebookLogin,
    isPending: isLoading,
    error: facebookLoginError,
  } = useMutation({
    mutationFn: async (accessToken: string) => {
      try {
        const response = await fetchAuth.facebookLogin(accessToken);
        if (!response || !response.status) {
          throw new Error(response?.message || 'Facebook login failed. Please try again.');
        }
        return response;
      } catch (err) {
        throw new Error(err instanceof Error ? err.message : 'Facebook login failed');
      }
    },
    onSuccess: (response: LoginResponse) => {
      if (response.status) {
        const token = response.data.accessToken;
        setToken(token);
        setCookie('auth-token', token, getAuthCookieConfig());
        queryClient.invalidateQueries({ queryKey: ['auth', 'facebookLogin'] });
        setFacebookLoginSuccess(true);
        // Handle redirect parameter
        const redirectTo = searchParams?.get('redirect');
        if (redirectTo) {
          router.push(redirectTo);
        } else {
          router.refresh();
        }
      } else {
        setError(response.message || 'Facebook login failed');
      }
    },
    onError: (error: Error) => {
      setError(error.message || 'Facebook login failed');
    },
  });

  return {
    facebookLogin,
    isLoading,
    error: error || facebookLoginError?.message || null,
    facebookLoginSuccess,
    clearError: () => setError(null),
  };
}

// Main auth hook that combines functionality
export function useAuth() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { logout: storeLogout, token, isAuthenticated } = useAuthStore();
  const loginHook = useLogin();
  const registerHook = useRegister();
  const googleLoginHook = useGoogleLogin();
  const verifyOtpHook = useVerifyOtp();
  const requestOtpHook = useRequestOtp();
  const forgotPassword = useForgotPassword();
  const renewPassword = useRenewPassword();
  const zaloLoginHook = useZaloLogin();
  const facebookLoginHook = useFacebookLogin();

  const { mutate: logout, isPending: isLoggingOut } = useMutation({
    mutationFn: () => fetchAuth.logout(),
    onSuccess: () => {
      // Clear both zustand store and cookies
      storeLogout();
      // Use same config for deletion as for setting
      const cookieConfig = getAuthCookieConfig();
      deleteCookie('auth-token', {
        path: cookieConfig.path,
        domain: cookieConfig.domain,
        secure: cookieConfig.secure,
      });
      localStorage.removeItem('chat_conversation');
      window.dispatchEvent(new Event('logout'));
      // Clear all cached data
      queryClient.clear(); // Clear all queries
      queryClient.removeQueries(); // Remove all queries from cache
      // Don't redirect to login if on pages that handle their own auth dialog
      const protectedPages = ['/myrevo', '/appointments'];
      const currentPath = window.location.pathname;
      const shouldShowDialog = protectedPages.some(page => currentPath.includes(page));

      if (!shouldShowDialog) {
        router.push('/login');
      }
      toast.success('Đăng xuất thành công');
    },
    onError: (error: Error) => {
      console.error('Logout failed:', error);
      // Still logout locally even if API call fails
      storeLogout();
      // Use same config for deletion as for setting
      const cookieConfig = getAuthCookieConfig();
      deleteCookie('auth-token', {
        path: cookieConfig.path,
        domain: cookieConfig.domain,
        secure: cookieConfig.secure,
      });
      localStorage.removeItem('chat_conversation');
      // Clear all cached data
      queryClient.clear(); // Clear all queries
      queryClient.removeQueries(); // Remove all queries from cache

      // Don't redirect to login if on pages that handle their own auth dialog
      const protectedPages = ['/myrevo', '/appointments'];
      const currentPath = window.location.pathname;
      const shouldShowDialog = protectedPages.some(page => currentPath.includes(page));

      if (!shouldShowDialog) {
        router.push('/login');
      }
      toast.success('Đăng xuất thành công');
    },
  });

  return {
    token,
    isAuthenticated,
    isLoading:
      loginHook.isLoading ||
      registerHook.isLoading ||
      isLoggingOut ||
      googleLoginHook.isLoading ||
      verifyOtpHook.isLoading ||
      requestOtpHook.isLoading ||
      forgotPassword.isLoading ||
      renewPassword.isLoading ||
      zaloLoginHook.isLoading ||
      facebookLoginHook.isLoading,
    error:
      loginHook.error ||
      registerHook.error ||
      googleLoginHook.error ||
      verifyOtpHook.error ||
      requestOtpHook.error ||
      forgotPassword.error ||
      renewPassword.error ||
      zaloLoginHook.error ||
      facebookLoginHook.error,

    login: loginHook.login,
    logout,
    register: registerHook.register,
    googleLogin: googleLoginHook.googleLogin,
    googleLoginSuccess: googleLoginHook.googleLoginSuccess,
    verifyOtp: verifyOtpHook.verifyOtp,
    requestOtp: requestOtpHook.requestOtp,
    forgotPassword: forgotPassword.forgotPassword,
    renewPassword: renewPassword.renewPassword,
    registerSuccess: registerHook.registerSuccess,
    keyVariable: registerHook.keyVariable,
    resetRegisterState: registerHook.resetRegisterState,

    needsOtpVerification: loginHook.needsOtpVerification,
    loginVerifyKey: loginHook.verifyKey,
    clearLoginOtpState: loginHook.clearOtpState,

    clearError: () => {
      loginHook.clearError();
      registerHook.clearError();
      googleLoginHook.clearError();
      verifyOtpHook.clearError();
      requestOtpHook.clearError();
      forgotPassword.clearError();
      renewPassword.clearError();
      zaloLoginHook.clearError();
      facebookLoginHook.clearError();
    },
    zaloLogin: zaloLoginHook.zaloLogin,
    zaloLoginLoading: zaloLoginHook.isLoading,
    zaloLoginError: zaloLoginHook.error,
    zaloLoginSuccess: zaloLoginHook.zaloLoginSuccess,
    facebookLogin: facebookLoginHook.facebookLogin,
    facebookLoginLoading: facebookLoginHook.isLoading,
    facebookLoginError: facebookLoginHook.error,
    facebookLoginSuccess: facebookLoginHook.facebookLoginSuccess,
  };
}

export function useOtpErrorHandler() {
  const [error, setError] = useState<string | null>(null);

  function handleError(error: string | object) {
    if (typeof error === 'string') {
      setError(error);
    } else if (error && typeof error === 'object' && 'message' in error) {
      setError((error as { message?: string }).message || 'Xác thực OTP thất bại');
    } else {
      setError('Xác thực OTP thất bại');
    }
  }

  return { error, setError, handleError };
}
