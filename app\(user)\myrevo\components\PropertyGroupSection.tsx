'use client';

import { PropertyCard } from '@/components/PropertyCard';
import { RecentlyViewedPropertyWithTime } from '@/hooks/useRecentlyViewed';

interface PropertyGroupSectionProps {
  title: string;
  properties: RecentlyViewedPropertyWithTime[];
  showCount?: boolean;
}

export function PropertyGroupSection({
  title,
  properties,
  showCount = true,
}: PropertyGroupSectionProps) {
  // Không render gì nếu không có properties
  if (properties.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* Heading với count */}
      <div className="flex items-center gap-2">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        {showCount && (
          <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
            {properties.length}
          </span>
        )}
      </div>

      {/* Properties grid */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-3">
        {properties.map(({ property, viewedAt, id }) => (
          <PropertyCard
            key={id}
            property={property}
            priority={false}
            size="md"
            viewedTime={viewedAt}
          />
        ))}
      </div>
    </div>
  );
}
