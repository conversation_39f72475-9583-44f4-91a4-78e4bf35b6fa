'use client';

import { LeadCard } from './leadCard';
import { useState, useEffect } from 'react';
import { Alert } from '@/components/ui/alert';
import { AlertCircle, TrendingUp, Star, Clock } from 'lucide-react';
import { AlertTitle } from '@/components/ui/alert';
import { AlertDescription } from '@/components/ui/alert';
import { Lead, LeadScore, LeadSearchParams } from '@/lib/api/services/fetchLead';
import { useLeads, useUpdateLeadScore } from '@/hooks/useLead';
import { LeadCardSkeleton } from '@/components/leadCardSeketon';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const LEAD_COLUMNS = [
  {
    id: 'Hot',
    title: 'Tiềm năng cao',
    description: '<PERSON>h<PERSON><PERSON> hàng có khả năng chuyển đổi cao',
    icon: TrendingUp,
    priority: 'Ưu tiên',
  },
  {
    id: 'Warm',
    title: '<PERSON>uan tâm',
    description: 'Khách hàng đang tìm hiểu và cân nhắc',
    icon: Star,
    priority: 'Trung bình',
  },
  {
    id: 'Cold',
    title: 'Tiềm năng thấp',
    description: 'Khách hàng cần được nuôi dưỡng thêm',
    icon: Clock,
    priority: 'Thấp',
  },
];

export function LeadGrid() {
  const [searchParams] = useState<LeadSearchParams>({
    pageNumber: 1,
    pageSize: 100,
  });

  const { data: leads, isLoading, isError, error, isFetching } = useLeads(searchParams);
  const updateLeadStatus = useUpdateLeadScore();

  // Store local state for optimistic updates
  const [localLeads, setLocalLeads] = useState<Lead[]>([]);

  // Update local state when API data changes
  useEffect(() => {
    if (leads?.leads) {
      setLocalLeads(leads.leads);
    }
  }, [leads]);

  const leadsArray = localLeads;

  const groupedLeads = {
    Hot: leadsArray.filter(lead => lead.score === LeadScore.Hot),
    Warm: leadsArray.filter(lead => lead.score === LeadScore.Warm),
    Cold: leadsArray.filter(lead => lead.score === LeadScore.Cold),
  };

  // Handle drag and drop event to update lead status
  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result;

    if (!destination) return;
    if (destination.droppableId === source.droppableId && destination.index === source.index) {
      return;
    }
    setLocalLeads(prevLeads =>
      prevLeads.map(lead =>
        lead.id === draggableId ? { ...lead, score: destination.droppableId as LeadScore } : lead
      )
    );
    updateLeadStatus.mutate(
      {
        leadId: draggableId,
        score: destination.droppableId as LeadScore,
      },
      {
        onError: () => {
          setLocalLeads(prevLeads =>
            prevLeads.map(lead =>
              lead.id === draggableId ? { ...lead, score: source.droppableId as LeadScore } : lead
            )
          );
        },
      }
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {LEAD_COLUMNS.map(column => (
            <Card key={column.id} className="border border-border/40 shadow-sm bg-muted">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-muted/50 flex items-center justify-center">
                      <column.icon className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <div>
                      <CardTitle className="text-base font-semibold">{column.title}</CardTitle>
                      <p className="text-xs text-muted-foreground mt-1">{column.description}</p>
                    </div>
                  </div>
                  <div className="h-6 w-12 bg-muted/30 rounded animate-pulse" />
                </div>
              </CardHeader>
              <CardContent className="p-4 space-y-3">
                {Array.from({ length: 2 }).map((_, index) => (
                  <LeadCardSkeleton key={index} />
                ))}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error?.message || 'Failed to load leads'}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {LEAD_COLUMNS.map(column => {
            const columnLeads = groupedLeads[column.id as LeadScore];
            return (
              <Card key={column.id} className="border border-border/40 shadow-sm bg-muted">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-lg bg-muted/50 flex items-center justify-center">
                        <column.icon className="h-5 w-5 text-muted-foreground" />
                      </div>
                      <div>
                        <CardTitle className="text-base font-semibold">{column.title}</CardTitle>
                        <p className="text-xs text-muted-foreground mt-1">{column.description}</p>
                      </div>
                    </div>
                    <Badge variant="outline" className="text-sm bg-muted/30">
                      {columnLeads.length}
                    </Badge>
                  </div>
                </CardHeader>
                <Droppable droppableId={column.id}>
                  {provided => (
                    <CardContent
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className="p-4 space-y-3 min-h-[400px]"
                    >
                      {columnLeads.length === 0 ? (
                        <div className="flex flex-col items-center justify-center py-12 text-center">
                          <div className="w-16 h-16 rounded-lg bg-muted/30 flex items-center justify-center mb-4">
                            <column.icon className="h-8 w-8 text-muted-foreground/60" />
                          </div>
                          <p className="text-sm font-medium text-muted-foreground">No leads yet</p>
                          <p className="text-xs text-muted-foreground mt-1">
                            Drag leads here to change their status
                          </p>
                        </div>
                      ) : (
                        columnLeads.map((lead, index) => (
                          <Draggable key={lead.id} draggableId={lead.id} index={index}>
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                style={provided.draggableProps.style}
                              >
                                <LeadCard
                                  lead={lead}
                                  status={column.id as LeadScore}
                                  isDragging={snapshot.isDragging}
                                  isLoading={isFetching}
                                />
                              </div>
                            )}
                          </Draggable>
                        ))
                      )}
                      {provided.placeholder}
                    </CardContent>
                  )}
                </Droppable>
              </Card>
            );
          })}
        </div>
      </DragDropContext>
    </div>
  );
}
