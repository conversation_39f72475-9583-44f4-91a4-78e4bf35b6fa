'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useCollections } from '@/hooks/useCollections';
import { CreateCollectionRequest } from '@/lib/api/services/fetchCollection';

interface CreateCollectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (collectionId: string) => void;
}

export default function CreateCollectionModal({
  isOpen,
  onClose,
  onSuccess,
}: CreateCollectionModalProps) {
  const [formData, setFormData] = useState<CreateCollectionRequest>({
    name: '',
    description: '',
  });

  const { createCollection, isCreatingCollection } = useCollections();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      return;
    }

    try {
      createCollection(formData, {
        onSuccess: () => {
          // Reset form
          setFormData({ name: '', description: '' });
          onClose();

          // Call success callback if provided - use temp ID since API doesn't return collection ID
          if (onSuccess) {
            onSuccess(`temp-${Date.now()}`);
          }
        },
      });
    } catch (error) {
      // Error is handled by the hook
    }
  };

  const handleClose = () => {
    if (!isCreatingCollection) {
      setFormData({ name: '', description: '' });
      onClose();
    }
  };

  const isNameValid = formData.name.trim().length > 0;
  const nameLength = formData.name.length;
  const descriptionLength = formData.description?.length || 0;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className="w-[90vw] max-w-sm sm:max-w-md p-0 gap-0 bg-white mx-auto"
        onCloseAutoFocus={e => e.preventDefault()}
      >
        {/* Header - Mobile optimized */}
        <DialogHeader className="px-4 sm:px-6 py-4 sm:py-5 border-b border-gray-100">
          <div className="pr-6 sm:pr-0">
            <DialogTitle className="text-base sm:text-lg font-medium text-gray-900 leading-tight">
              Tạo bộ sưu tập mới
            </DialogTitle>
            <DialogDescription className="text-sm text-gray-600 mt-1">
              Tạo một bộ sưu tập để sắp xếp các bất động sản yêu thích của bạn.
            </DialogDescription>
          </div>
        </DialogHeader>

        {/* Form Content - Mobile-first responsive */}
        <form onSubmit={handleSubmit} className="flex flex-col">
          <div className="px-4 sm:px-6 py-4 sm:py-6 space-y-5 sm:space-y-6 flex-1">
            {/* Name Field - Mobile-friendly */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="collection-name" className="text-sm font-medium text-gray-700">
                  Tên bộ sưu tập
                </Label>
                <span className="text-xs text-gray-400 font-mono tabular-nums">
                  {nameLength}/64
                </span>
              </div>
              <Input
                id="collection-name"
                type="text"
                value={formData.name}
                onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
                maxLength={64}
                placeholder="Nhập tên bộ sưu tập"
                disabled={isCreatingCollection}
                className="w-full h-11 sm:h-10 text-base sm:text-sm border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                autoFocus
              />
              {/* Character count feedback for mobile */}
              <div className="sm:hidden">
                <div
                  className={`text-xs mt-1 ${nameLength > 60 ? 'text-orange-500' : 'text-gray-400'}`}
                >
                  {nameLength > 0 && `${nameLength} ký tự`}
                </div>
              </div>
            </div>

            {/* Description Field - Mobile-optimized */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label
                  htmlFor="collection-description"
                  className="text-sm font-medium text-gray-700"
                >
                  Mô tả (tùy chọn)
                </Label>
                <span className="text-xs text-gray-400 font-mono tabular-nums">
                  {descriptionLength}/160
                </span>
              </div>
              <Textarea
                id="collection-description"
                value={formData.description || ''}
                onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
                maxLength={160}
                placeholder="Nhập mô tả bộ sưu tập"
                disabled={isCreatingCollection}
                className="w-full min-h-[88px] sm:min-h-[80px] text-base sm:text-sm border-gray-200 focus:border-blue-500 focus:ring-blue-500 resize-none"
                rows={3}
              />
              {/* Help text - Mobile-friendly */}
              <p className="text-xs text-gray-500 leading-relaxed">
                URL sẽ được chuyển thành liên kết. Chỉ cho phép thẻ &lt;a&gt;
              </p>
              {/* Character count feedback for mobile */}
              <div className="sm:hidden">
                <div
                  className={`text-xs ${descriptionLength > 150 ? 'text-orange-500' : 'text-gray-400'}`}
                >
                  {descriptionLength > 0 && `${descriptionLength} ký tự`}
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons - Mobile-optimized layout */}
          <div className="px-4 sm:px-6 py-4 sm:py-5 border-t border-gray-100">
            <div className="flex flex-col-reverse sm:flex-row gap-3 sm:gap-0 sm:items-center sm:justify-end">
              <Button
                type="button"
                variant="ghost"
                onClick={handleClose}
                disabled={isCreatingCollection}
                className="w-full sm:w-auto h-11 sm:h-auto sm:px-4 sm:py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 font-medium"
              >
                Hủy
              </Button>
              <Button
                type="submit"
                disabled={!isNameValid || isCreatingCollection}
                className="w-full sm:w-auto h-11 sm:h-auto sm:px-6 sm:py-2 sm:ml-3 bg-gray-900 hover:bg-gray-800 text-white disabled:bg-gray-300 disabled:cursor-not-allowed font-medium"
              >
                {isCreatingCollection ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Đang tạo...</span>
                  </div>
                ) : (
                  'Tạo bộ sưu tập'
                )}
              </Button>
            </div>

            {/* Mobile-only validation feedback */}
            <div className="sm:hidden mt-3">
              {formData.name.length === 0 && (
                <p className="text-xs text-gray-500">Vui lòng nhập tên bộ sưu tập để tiếp tục</p>
              )}
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
