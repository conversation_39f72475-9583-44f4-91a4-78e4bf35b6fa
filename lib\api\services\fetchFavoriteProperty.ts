import apiService from '../core';
import { Property } from './fetchProperty';

export interface FavoritePropertyRequest {
  propertyId: string;
  savedAt: string;
}

export interface FavoritePropertyResponse {
  code: number;
  status: boolean;
  message: string;
  data: {
    propertyId: string;
  } | null;
}

export interface FavoritePropertiesResponse {
  code: number;
  status: boolean;
  message: string;
  data: PropertyListResponse[];
}
interface PropertyListResponse {
  properties: Property[];
  page: number;
  limit: number;
  count: number;
  totalPages: number;
}

export const favoritePropertyService = {
  // Add a property to favorites
  addFavoriteProperty: async (
    request: FavoritePropertyRequest
  ): Promise<FavoritePropertyResponse> => {
    const response = await apiService.post<FavoritePropertyResponse, FavoritePropertyRequest>(
      '/api/favorite-properties',
      request
    );
    return response.data;
  },

  // Remove a property from favorites
  removeFavoriteProperty: async (propertyId: string): Promise<FavoritePropertyResponse> => {
    const response = await apiService.delete<FavoritePropertyResponse>(
      `/api/favorite-properties/${propertyId}`
    );
    return response.data;
  },

  // Get all favorite properties for the current user
  getFavoriteProperties: async (): Promise<FavoritePropertiesResponse> => {
    const response = await apiService.get<FavoritePropertiesResponse>(
      '/api/favorite-properties/user'
    );
    return response.data;
  },
};

export default favoritePropertyService;
