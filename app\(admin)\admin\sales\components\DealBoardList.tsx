import React, { useState } from 'react';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { useRouter } from 'next/navigation';
import {
  Eye,
  Loader2,
  MoreHorizontal,
  Trash2,
  Plus,
  ChevronDown,
  ChevronRight,
  Circle,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { priorityConfig, statusConfig, columnConfig } from '../config/configuration';

import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

import { Deal, DealSearchParams } from '@/lib/api/services/fetchDeal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { DeleteDealDialog } from '@/app/(admin)/admin/sales/components/DeleteDealDialog';
import { useDeleteDealWithConfirm } from '../custom-hooks/useDeleteDealWithConfirm';

// Group deals by status
const groupDealsByStatus = (deals: Deal[]) => {
  const grouped = deals.reduce(
    (acc, deal) => {
      const status = deal.status || 'New';
      if (!acc[status]) {
        acc[status] = [];
      }
      acc[status].push(deal);
      return acc;
    },
    {} as Record<string, Deal[]>
  );

  return grouped;
};

interface DealBoardListProps {
  data: { deals: Deal[]; count: number } | undefined;
  isLoading: boolean;
  error: Error | null;
  filters: DealSearchParams;
  pagination: { pageNumber: number; pageSize: number };
  handleFilterChange: (key: keyof DealSearchParams, value: string | boolean) => void;
  handlePageChange: (page: number) => void;
  handlePageSizeChange: (size: number) => void;
}

export default function DealBoardList({
  data,
  isLoading,
  error,
  pagination,
  handlePageChange,
}: DealBoardListProps) {
  const router = useRouter();
  const { dealToDelete, handleDeleteClick, confirmDelete, closeDeleteDialog, isDeleting } =
    useDeleteDealWithConfirm();

  // Initialize expanded groups using columnConfig
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>(
    Object.fromEntries(columnConfig.map(col => [col.id, true]))
  );

  // Handle view deal details
  const handleViewDealDetails = (dealId: string) => {
    router.push(`sales/${dealId}`);
  };

  // Toggle group expansion
  const toggleGroup = (status: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [status]: !prev[status],
    }));
  };

  if (isLoading) {
    return (
      <Card className="w-full p-8">
        <div className="flex items-center justify-center">
          <p className="text-muted-foreground">Loading deals...</p>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full p-8">
        <div className="flex items-center justify-center">
          <p className="text-red-500">Error loading deals. Please try again.</p>
        </div>
      </Card>
    );
  }

  const groupedDeals = groupDealsByStatus(data?.deals || []);

  return (
    <div className="space-y-4 bg-gray-50 min-h-screen p-4">
      {/* Deals List */}
      <div className="space-y-1">
        {Object.entries(statusConfig).map(([status, config]) => {
          const deals = groupedDeals[status] || [];
          const isExpanded = expandedGroups[status];

          return (
            <div key={status} className="bg-white rounded-lg shadow-sm border overflow-hidden">
              {/* Group Header */}
              <div
                className="flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer border-l-4"
                style={{ borderLeftColor: config.color }}
                onClick={() => toggleGroup(status)}
              >
                <div className="flex items-center gap-3">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  )}

                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: config.color }} />

                  <span className="font-medium text-gray-900">{config.label.toUpperCase()}</span>

                  <Badge variant="secondary" className="text-xs">
                    {deals.length}
                  </Badge>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-gray-200"
                  onClick={e => {
                    e.stopPropagation();
                    // Add new deal logic
                  }}
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>

              {/* Group Content */}
              {isExpanded && (
                <div className="border-t border-gray-100">
                  {deals.length === 0 ? (
                    <div className="p-8 text-center text-gray-500">
                      <Circle className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                      <p>No deals in {config.label}</p>
                    </div>
                  ) : (
                    <div className="divide-y divide-gray-100">
                      {deals.map((deal: Deal) => (
                        <div
                          key={deal.id}
                          className="group flex items-center gap-4 p-3 hover:bg-gray-50 cursor-pointer transition-colors"
                          onClick={() => handleViewDealDetails(deal.id)}
                        >
                          {/* Checkbox */}
                          <div className="flex-shrink-0">
                            <Circle className="h-4 w-4 text-gray-400 group-hover:text-gray-600" />
                          </div>

                          {/* Deal Content */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-3">
                              <h3 className="font-medium text-gray-900 truncate">
                                {deal.title || 'Untitled Deal'}
                              </h3>

                              {/* Priority Badge */}
                              {deal.priority && (
                                <Badge
                                  className={`text-xs ${
                                    priorityConfig.list[
                                      deal.priority.toUpperCase() as keyof typeof priorityConfig.list
                                    ]?.className || 'bg-gray-100 text-gray-800'
                                  }`}
                                >
                                  {priorityConfig.list[
                                    deal.priority.toUpperCase() as keyof typeof priorityConfig.list
                                  ]?.label || deal.priority}
                                </Badge>
                              )}

                              {/* Tags */}
                              <div className="flex gap-1">
                                <Badge
                                  variant="outline"
                                  className="text-xs bg-blue-50 text-blue-700 border-blue-200"
                                >
                                  rental
                                </Badge>
                                <Badge
                                  variant="outline"
                                  className="text-xs bg-green-50 text-green-700 border-green-200"
                                >
                                  vinhomes
                                </Badge>
                              </div>
                            </div>

                            {/* Description */}
                            {deal.description && (
                              <p className="text-sm text-gray-600 mt-1 truncate">
                                {deal.description}
                              </p>
                            )}
                          </div>

                          {/* Right Side Info */}
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            {/* Customer */}
                            <div className="hidden md:block min-w-0">
                              <span className="truncate">
                                {deal.customer?.name || 'Unassigned'}
                              </span>
                            </div>

                            {/* Date */}
                            <div className="hidden lg:block">
                              {deal.updatedAt && format(new Date(deal.updatedAt), 'MMM dd')}
                            </div>

                            {/* Avatar */}
                            <div className="flex-shrink-0">
                              <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                                {deal.customer?.name?.charAt(0)?.toUpperCase() || 'U'}
                              </div>
                            </div>

                            {/* Actions */}
                            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 hover:bg-gray-200"
                                onClick={e => {
                                  e.stopPropagation();
                                  handleViewDealDetails(deal.id);
                                }}
                              >
                                <Eye className="h-3 w-3" />
                              </Button>

                              <DropdownMenu modal={false}>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 hover:bg-gray-200"
                                    onClick={e => e.stopPropagation()}
                                  >
                                    <MoreHorizontal className="h-3 w-3" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="w-40">
                                  <DropdownMenuItem
                                    onClick={e => {
                                      e.stopPropagation();
                                      handleViewDealDetails(deal.id);
                                    }}
                                  >
                                    <Eye className="mr-2 h-3 w-3" />
                                    View
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={e => handleDeleteClick(deal, e)}
                                    className="text-red-600 focus:text-red-600"
                                  >
                                    {isDeleting ? (
                                      <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                                    ) : (
                                      <Trash2 className="mr-2 h-3 w-3" />
                                    )}
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Pagination */}
      {data && Math.ceil(data.count / pagination.pageSize) > 1 && (
        <div className="flex justify-center mt-6">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => handlePageChange(pagination.pageNumber - 1)}
                  className={
                    pagination.pageNumber === 1
                      ? 'pointer-events-none opacity-50'
                      : 'cursor-pointer'
                  }
                />
              </PaginationItem>

              {Array.from(
                { length: Math.ceil(data.count / pagination.pageSize) },
                (_, i) => i + 1
              ).map(page => (
                <PaginationItem key={page}>
                  <PaginationLink
                    onClick={() => handlePageChange(page)}
                    isActive={pagination.pageNumber === page}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  onClick={() => handlePageChange(pagination.pageNumber + 1)}
                  className={
                    pagination.pageNumber === Math.ceil(data.count / pagination.pageSize)
                      ? 'pointer-events-none opacity-50'
                      : 'cursor-pointer'
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      <DeleteDealDialog
        deal={dealToDelete}
        open={!!dealToDelete}
        onOpenChange={closeDeleteDialog}
        onConfirm={confirmDelete}
        isDeleting={isDeleting}
      />
    </div>
  );
}
