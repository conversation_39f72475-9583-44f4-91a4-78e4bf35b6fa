import { Metadata } from 'next';
import { propertyService, Property } from '@/lib/api/services/fetchProperty';
import { formatCurrency } from '@/utils/numbers/formatCurrency';
import PropertyDetailClient from './component/PropertyDetailClient';

export interface PropertyDetailPageProps {
  params: { id: string };
}

export async function generateMetadata({ params }: PropertyDetailPageProps): Promise<Metadata> {
  try {
    const response = await propertyService.getProperty(params.id);
    const property = response.data;
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://revoland.com';

    if (!property) {
      return {
        title: 'Bất động sản không tồn tại - RevoLand',
        description: 'Không tìm thấy bất động sản này',
        robots: {
          index: false,
          follow: false,
        },
      };
    }

    // Construct the property URL
    const propertyUrl = `${baseUrl}/properties/${property.id}`;

    // Generate price information
    const priceInfo = generatePriceInfo(property);
    const locationInfo = generateLocationInfo(property);
    const propertyTypeInfo = generatePropertyTypeInfo(property);

    // Create SEO-friendly title and description
    const title = `${property.title} - ${locationInfo} | RevoLand`;
    const description = `${property.description.substring(0, 160)}${property.description.length > 160 ? '...' : ''} ${priceInfo} tại ${locationInfo}. ${propertyTypeInfo}`;

    // Get the first image for Open Graph
    const mainImage = property.imageUrls?.[0] || '/hero.jpg';

    return {
      title,
      description,
      openGraph: {
        type: 'website',
        title,
        description,
        images: [
          {
            url: mainImage,
            width: 1200,
            height: 630,
            alt: property.title,
          },
        ],
        siteName: 'RevoLand',
        locale: 'vi_VN',
        url: propertyUrl,
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: [mainImage],
        site: '@RevoLand',
      },
      alternates: {
        canonical: propertyUrl,
      },
      keywords: [
        property.title,
        property.type,
        property.transactionType,
        property.location.city,
        property.location.district,
        'bất động sản',
        'mua bán nhà đất',
        'cho thuê bất động sản',
        'RevoLand',
        ...generateKeywords(property),
      ],
      creator: 'RevoLand',
      publisher: 'RevoLand',
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      other: {
        'property:price:amount':
          property.priceDetails.salePrice?.toString() ||
          property.priceDetails.rentalPrice?.toString() ||
          '',
        'property:price:currency': property.priceDetails.currency,
        'property:location:latitude': property.location.latitude.toString(),
        'property:location:longitude': property.location.longitude.toString(),
        'property:type': property.type,
        'property:transaction_type': property.transactionType,
        'property:bedrooms': property.propertyDetails.bedrooms?.toString() || '',
        'property:bathrooms': property.propertyDetails.bathrooms?.toString() || '',
        'property:land_area': property.propertyDetails.landArea?.toString() || '',
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Bất động sản - RevoLand',
      description: 'Khám phá các bất động sản chất lượng tại RevoLand',
    };
  }
}

function generatePriceInfo(property: Property): string {
  const { priceDetails } = property;

  if (property.transactionType === 'ForSale' && priceDetails.salePrice) {
    return `Giá bán: ${formatCurrency(priceDetails.salePrice, priceDetails.currency)}`;
  } else if (property.transactionType === 'ForRent' && priceDetails.rentalPrice) {
    return `Giá thuê: ${formatCurrency(priceDetails.rentalPrice, priceDetails.currency)}/tháng`;
  } else if (priceDetails.pricePerSquareMeter) {
    return `Giá: ${formatCurrency(priceDetails.pricePerSquareMeter, priceDetails.currency)}/m²`;
  }

  return 'Liên hệ để biết giá';
}

function generateLocationInfo(property: Property): string {
  const { location } = property;
  const parts = [location.ward, location.district, location.city].filter(Boolean);
  return parts.join(', ');
}

function generatePropertyTypeInfo(property: Property): string {
  const { type, propertyDetails } = property;

  let info = `Loại: ${type}`;

  if (propertyDetails.bedrooms) {
    info += `, ${propertyDetails.bedrooms} phòng ngủ`;
  }
  if (propertyDetails.bathrooms) {
    info += `, ${propertyDetails.bathrooms} phòng tắm`;
  }
  if (propertyDetails.landArea) {
    info += `, ${propertyDetails.landArea}m²`;
  }

  return info;
}

function generateKeywords(property: Property): string[] {
  const keywords: string[] = [];

  // Add property type keywords
  keywords.push(property.type);

  // Add transaction type keywords
  if (property.transactionType === 'ForSale') {
    keywords.push('mua bán', 'bán nhà', 'bán đất');
  } else if (property.transactionType === 'ForRent') {
    keywords.push('cho thuê', 'thuê nhà', 'thuê đất');
  }

  // Add location keywords
  keywords.push(property.location.city, property.location.district, property.location.ward);

  // Add property details keywords
  if (property.propertyDetails.bedrooms) {
    keywords.push(`${property.propertyDetails.bedrooms} phòng ngủ`);
  }
  if (property.propertyDetails.bathrooms) {
    keywords.push(`${property.propertyDetails.bathrooms} phòng tắm`);
  }
  if (property.propertyDetails.landArea) {
    keywords.push(`${property.propertyDetails.landArea}m²`);
  }

  // Add amenities keywords
  const amenities = property.amenities;
  if (amenities.parking) keywords.push('có chỗ đậu xe');
  if (amenities.elevator) keywords.push('có thang máy');
  if (amenities.swimmingPool) keywords.push('có hồ bơi');
  if (amenities.gym) keywords.push('có phòng gym');
  if (amenities.securitySystem) keywords.push('có bảo vệ');
  if (amenities.airConditioning) keywords.push('có điều hòa');
  if (amenities.balcony) keywords.push('có ban công');
  if (amenities.garden) keywords.push('có vườn');

  return keywords;
}

export default async function PropertyDetailPage({ params }: PropertyDetailPageProps) {
  return <PropertyDetailClient propertyId={params.id} />;
}
