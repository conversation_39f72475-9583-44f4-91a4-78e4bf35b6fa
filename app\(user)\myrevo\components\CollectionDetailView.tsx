import { useState, useEffect } from 'react';
import { PropertyCard } from '@/components/PropertyCard';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useCollections } from '@/hooks/useCollections';
import { Collection } from '@/lib/api/services/fetchCollection';
import { Property } from '@/lib/api/services/fetchProperty';
import { Image as ImageIcon, ArrowLeft, Folder, Trash } from 'lucide-react';
import DeleteConfirmationDialog from '@/components/collection/DeleteConfirmationDialog';
import Image from 'next/image';

interface CollectionThumbnailProps {
  images: string[];
  collectionName: string;
  className?: string;
}

function CollectionThumbnail({ images, collectionName, className = '' }: CollectionThumbnailProps) {
  const placeholderImages = Array(4).fill(null);

  return (
    <div className={`w-12 h-12 rounded-lg overflow-hidden bg-gray-100 ${className}`}>
      {images.length === 0 ? (
        // No images - show folder icon
        <div className="w-full h-full flex items-center justify-center">
          <Folder className="w-6 h-6 text-gray-400" />
        </div>
      ) : images.length === 1 ? (
        // 1 image - show single image
        <div className="w-full h-full relative">
          <Image src={images[0]} alt={collectionName} fill className="object-cover" sizes="48px" />
        </div>
      ) : (
        // Multiple images - show 4-image grid layout
        <div className="w-full h-full grid grid-cols-2 gap-0.5">
          {placeholderImages.map((_, index) => (
            <div key={index} className="relative bg-gray-200">
              {images[index] ? (
                <Image
                  src={images[index]}
                  alt={`${collectionName} - ${index + 1}`}
                  fill
                  className="object-cover"
                  sizes="24px"
                />
              ) : (
                <div className="w-full h-full bg-gray-100" />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

interface CollectionDetailViewProps {
  collection: Collection;
  onBack: () => void;
}

export default function CollectionDetailView({ collection, onBack }: CollectionDetailViewProps) {
  const [properties, setProperties] = useState<Property[]>([]);
  const [removingProperty, setRemovingProperty] = useState<{ id: string; name: string } | null>(
    null
  );
  const { useCollectionWithProperties, removeFromCollection, isRemovingFromCollection } =
    useCollections();

  // Get collection with properties
  const {
    data: collectionWithProperties,
    isLoading: isLoadingProperties,
    error: propertiesError,
  } = useCollectionWithProperties(collection.id);

  useEffect(() => {
    if (collectionWithProperties) {
      setProperties(collectionWithProperties.properties || []);
    }
  }, [collectionWithProperties]);

  const handleRemoveProperty = (propertyId: string, propertyName: string) => {
    setRemovingProperty({ id: propertyId, name: propertyName });
  };

  const handleConfirmRemove = () => {
    if (removingProperty) {
      removeFromCollection({ collectionId: collection.id, propertyId: removingProperty.id });
      setRemovingProperty(null);
    }
  };

  const handleCancelRemove = () => {
    setRemovingProperty(null);
  };

  if (isLoadingProperties) {
    return (
      <div className="space-y-4 sm:space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-6 w-64" />
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="space-y-3">
              <Skeleton className="aspect-square w-full rounded-xl" />
              <div className="space-y-2 px-1">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (propertiesError) {
    return (
      <div className="text-center py-16 sm:py-20">
        <div className="mx-auto max-w-md px-4">
          <div className="text-red-500 mb-4">
            <svg
              className="h-16 w-16 mx-auto"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833-.23 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Có lỗi xảy ra</h3>
          <p className="text-sm text-gray-500 mb-6">
            Không thể tải dữ liệu bộ sưu tập. Vui lòng thử lại sau.
          </p>
          <Button onClick={onBack} variant="outline" className="w-full sm:w-auto">
            Quay lại
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Mobile-optimized Header */}
      <div className="space-y-4">
        {/* Back button - Mobile optimized */}
        <Button
          variant="ghost"
          onClick={onBack}
          className="flex items-center gap-2 -ml-2 sm:-ml-4 px-2 sm:px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg"
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="text-sm sm:text-base">Quay lại bộ sưu tập</span>
        </Button>

        {/* Title and description with thumbnail */}
        <div className="space-y-3">
          <div className="flex items-center gap-4">
            <CollectionThumbnail
              images={collection.collectionImage || []}
              collectionName={collection.name}
              className="w-16 h-16 sm:w-20 sm:h-20"
            />
            <div className="flex-1">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold leading-tight">
                {collection.name}
              </h1>
              <div className="flex flex-col xs:flex-row xs:items-center gap-2 xs:gap-4 mt-2">
                <Badge variant="secondary" className="w-fit px-3 py-1">
                  {collection.itemCount} bất động sản
                </Badge>
                <span className="text-xs sm:text-sm text-gray-500">
                  Cập nhật {new Date(collection.updatedAt).toLocaleDateString('vi-VN')}
                </span>
              </div>
            </div>
          </div>
          {collection.description && (
            <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
              {collection.description}
            </p>
          )}
        </div>
      </div>

      {/* Properties Grid - Mobile-first responsive */}
      {properties.length === 0 ? (
        <div className="text-center py-16 sm:py-20">
          <div className="mx-auto max-w-md px-4">
            <ImageIcon className="h-16 w-16 sm:h-20 sm:w-20 text-gray-300 mx-auto mb-6" />
            <h3 className="text-lg sm:text-xl font-medium text-gray-900 mb-2">
              Chưa có bất động sản nào
            </h3>
            <p className="text-sm sm:text-base text-gray-500 leading-relaxed">
              Bộ sưu tập này chưa có bất động sản nào. Hãy thêm một số bất động sản yêu thích của
              bạn!
            </p>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6">
          {properties.map((property: Property) => (
            <div key={property.id} className="relative group">
              <PropertyCard property={property} size="md" />
              <Button
                variant="destructive"
                size="icon"
                className="absolute top-2 right-2 h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                onClick={() => handleRemoveProperty(property.id, property.title)}
              >
                <Trash className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}

      <DeleteConfirmationDialog
        isOpen={!!removingProperty}
        onClose={handleCancelRemove}
        onConfirm={handleConfirmRemove}
        title="Xóa bất động sản"
        description="Bạn có chắc chắn muốn xóa bất động sản này khỏi bộ sưu tập không?"
        itemName={removingProperty?.name}
        type="property"
        isLoading={isRemovingFromCollection}
      />
    </div>
  );
}
