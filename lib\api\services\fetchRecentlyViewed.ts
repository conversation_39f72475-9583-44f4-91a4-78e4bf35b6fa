import apiService from '../core';

// Response types for Recently Viewed API
export interface SyncRecentlyViewedResponse {
  code: number;
  status: boolean;
  message: string;
  data?: string;
}

export interface FetchRecentlyViewedResponse {
  code: number;
  status: boolean;
  message: string;
  data?: string[] | any; // Flexible typing for different server response formats
}

/**
 * API Service for Recently Viewed Properties
 * Handles sync and fetch operations with the server
 */
export const recentlyViewedService = {
  /**
   * Sync recently viewed property IDs to server
   */
  syncRecentlyViewed: async (propertyIds: string[]): Promise<SyncRecentlyViewedResponse> => {
    try {
      console.log('📤 Syncing property IDs to server:', propertyIds);

      const response = await apiService.post<SyncRecentlyViewedResponse, string[]>(
        '/api/PropertyViewHistory',
        propertyIds
      );

      console.log('✅ Sync response received:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to sync recently viewed to server:', error);
      throw error;
    }
  },

  /**
   * <PERSON>tch recently viewed property IDs from server
   */
  fetchRecentlyViewed: async (): Promise<string[]> => {
    try {
      console.log('🌐 Fetching recently viewed from server...');

      const response = await apiService.get<FetchRecentlyViewedResponse>(
        '/api/PropertyViewHistory'
      );

      console.log('🔄 Server response received:', response.data);

      if (!response.data.status || !response.data.data) {
        console.log('⚠️ Server returned no data or error:', response.data.message);
        return [];
      }

      return parseServerPropertyIds(response.data.data);
    } catch (error) {
      console.error('❌ Failed to fetch recently viewed from server:', error);
      return [];
    }
  },
};

/**
 * Parse property IDs from server response data
 * Handles multiple formats that the server might return
 */
function parseServerPropertyIds(data: any): string[] {
  if (!data) {
    console.log('⚠️ No data to parse');
    return [];
  }

  // Handle array responses
  if (Array.isArray(data)) {
    return parseArrayResponse(data);
  }

  // Handle other formats (string, object, etc.)
  console.log('⚠️ Unexpected server response format:', typeof data, data);
  return [];
}

/**
 * Parse array response from server
 * Supports different array item formats (strings, objects with IDs)
 */
function parseArrayResponse(dataArray: any[]): string[] {
  if (dataArray.length === 0) {
    console.log('📝 Server returned empty array');
    return [];
  }

  const firstItem = dataArray[0];

  // Case 1: Array of strings (property IDs)
  if (typeof firstItem === 'string') {
    console.log('✅ Received property IDs as strings:', dataArray);
    return dataArray.filter(id => typeof id === 'string' && id.trim().length > 0);
  }

  // Case 2: Array of objects with property IDs
  if (typeof firstItem === 'object' && firstItem !== null) {
    console.log('🔍 Received objects, extracting property IDs...');
    return extractPropertyIdsFromObjects(dataArray);
  }

  console.log('⚠️ Unsupported array item format:', typeof firstItem);
  return [];
}

/**
 * Extract property IDs from array of objects
 * Tries common property ID field names
 */
function extractPropertyIdsFromObjects(objects: any[]): string[] {
  const possibleIdFields = ['propertyId', 'id', '_id', 'property_id'];

  // Find which field contains the property ID
  const firstItem = objects[0];
  const idField = possibleIdFields.find(
    field => firstItem[field] && typeof firstItem[field] === 'string'
  );

  if (!idField) {
    console.log('⚠️ No valid property ID field found in objects:', Object.keys(firstItem));
    return [];
  }

  // Extract property IDs using the found field
  const propertyIds = objects
    .map(item => item[idField])
    .filter(id => typeof id === 'string' && id.trim().length > 0);

  console.log(`✅ Extracted property IDs using field "${idField}":`, propertyIds);
  return propertyIds;
}
