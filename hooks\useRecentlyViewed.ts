import { useState, useEffect, useCallback, useRef } from 'react';
import { Property } from '@/lib/api/services/fetchProperty';
import {
  RecentlyViewedStorage,
  RecentlyViewedStorageItem,
} from '@/lib/storage/recentlyViewedStorage';
import { RECENTLY_VIEWED_CONFIG } from '@/lib/config/recentlyViewed';
import { useSyncRecentlyViewed, useFetchRecentlyViewed } from '@/hooks/useRecentlyViewedApi';
import { useAuthStore } from '@/lib/store/authStore';

export interface RecentlyViewedPropertyWithTime {
  property: Property;
  viewedAt: string;
  propertyId: string;
  id: string;
}

interface UseRecentlyViewedReturn {
  recentlyViewed: RecentlyViewedStorageItem[];
  recentlyViewedProperties: Property[];
  allRecentlyViewedProperties: Property[];
  recentlyViewedWithTime: RecentlyViewedPropertyWithTime[];
  allRecentlyViewedWithTime: RecentlyViewedPropertyWithTime[];
  isLoading: boolean;
  error: string | null;
  addToRecentlyViewed: (property: Property) => void;
  removeFromRecentlyViewed: (propertyId: string) => void;
  clearRecentlyViewed: () => void;
  refreshRecentlyViewed: () => RecentlyViewedStorageItem[];
  hasRecentlyViewed: boolean;
  isProcessing: boolean;
  hasSynced: boolean;
}

interface UseRecentlyViewedOptions {
  enableSync?: boolean; // Enable server sync (default: true)
  serverOnly?: boolean; // For /myrevo page (default: false)
}

/**
 * Unified hook for recently viewed properties
 * Handles localStorage operations and server sync
 *
 * @example
 * // Basic usage
 * const { allRecentlyViewedProperties, addToRecentlyViewed } = useRecentlyViewed();
 *
 * // For /myrevo page
 * const { allRecentlyViewedProperties } = useRecentlyViewed({ serverOnly: true });
 */
export function useRecentlyViewed(options: UseRecentlyViewedOptions = {}): UseRecentlyViewedReturn {
  const { enableSync = true } = options;

  const [recentlyViewed, setRecentlyViewed] = useState<RecentlyViewedStorageItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const storageRef = useRef<RecentlyViewedStorage | null>(null);

  // Sync state
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const hasSyncedRef = useRef(false);
  const isProcessingRef = useRef(false);

  // Initialize storage once
  if (!storageRef.current) {
    storageRef.current = new RecentlyViewedStorage();
  }

  // API hooks
  const syncMutation = useSyncRecentlyViewed();
  const shouldFetchFromServer =
    enableSync && isAuthenticated && !hasSyncedRef.current && !isProcessingRef.current;
  const { data: serverPropertyIds, isLoading: isLoadingServer } =
    useFetchRecentlyViewed(shouldFetchFromServer);

  // Reload from localStorage
  const reloadFromStorage = useCallback((): RecentlyViewedStorageItem[] => {
    try {
      const items = storageRef.current!.getItems();
      setRecentlyViewed(items);
      setError(null);
      return items;
    } catch (err) {
      console.error('Failed to load recently viewed properties:', err);
      setError('Unable to load recently viewed properties');
      return [];
    }
  }, []);

  // Initial load on mount
  useEffect(() => {
    console.log('🔄 Initializing recently viewed from localStorage');
    reloadFromStorage();
    setIsLoading(false);
  }, []);

  // Merge localStorage + server data when user logs in
  useEffect(() => {
    if (
      !enableSync ||
      !isAuthenticated ||
      hasSyncedRef.current ||
      isProcessingRef.current ||
      serverPropertyIds === undefined
    ) {
      return;
    }

    isProcessingRef.current = true;

    const mergeAndSync = async (): Promise<void> => {
      try {
        const localItems = storageRef.current!.getItems();
        const localPropertyIds = localItems.map(item => item.propertyId);

        console.log('🔄 Starting merge process:', {
          localCount: localPropertyIds.length,
          serverCount: serverPropertyIds.length,
        });

        // Create merged unique IDs
        const uniqueIds = new Set([...localPropertyIds, ...serverPropertyIds]);
        const allPropertyIds = Array.from(uniqueIds);

        if (allPropertyIds.length === 0) {
          console.log('⭕ No data to merge');
          hasSyncedRef.current = true;
          return;
        }

        // Fetch missing properties from server
        const missingFromLocal = serverPropertyIds.filter(id => !localPropertyIds.includes(id));

        if (missingFromLocal.length > 0) {
          console.log('📥 Fetching missing properties from server:', missingFromLocal);
          await fetchAndAddMissingProperties(missingFromLocal);
        }

        // Sync merged data to server
        if (allPropertyIds.length > 0) {
          console.log('📤 Syncing merged data to server:', allPropertyIds);
          await syncMutation.mutateAsync(allPropertyIds);
          console.log('✅ Merged data synced to server successfully');
        }

        hasSyncedRef.current = true;
        console.log('✅ Merge completed - Eye icons will display for all viewed properties');
      } catch (error) {
        console.error('❌ Error during merge and sync process:', error);
        hasSyncedRef.current = true;
      } finally {
        isProcessingRef.current = false;
      }
    };

    const fetchAndAddMissingProperties = async (missingPropertyIds: string[]): Promise<void> => {
      try {
        const { default: propertyService } = await import('@/lib/api/services/fetchProperty');

        const propertyPromises = missingPropertyIds.map(
          async (propertyId): Promise<Property | null> => {
            try {
              const response = await propertyService.getProperty(propertyId);
              return response.status && response.data ? response.data : null;
            } catch (error) {
              console.error(`❌ Error fetching property ${propertyId}:`, error);
              return null;
            }
          }
        );

        const fetchedProperties = await Promise.all(propertyPromises);
        const validProperties = fetchedProperties.filter(
          (property): property is Property => property !== null
        );

        // Add to localStorage
        for (const property of validProperties) {
          try {
            storageRef.current!.addItem(property);
          } catch (error) {
            console.error(`❌ Error adding property ${property.id} to localStorage:`, error);
          }
        }

        reloadFromStorage();
        console.log(
          `📊 Successfully added ${validProperties.length}/${missingPropertyIds.length} missing properties`
        );
      } catch (error) {
        console.error('❌ Error fetching missing properties:', error);
      }
    };

    mergeAndSync();
  }, [enableSync, isAuthenticated, serverPropertyIds, syncMutation, reloadFromStorage]);

  // Reset sync flags on logout
  useEffect(() => {
    if (!isAuthenticated) {
      hasSyncedRef.current = false;
      isProcessingRef.current = false;
    }
  }, [isAuthenticated]);

  // Add property to recently viewed
  const addToRecentlyViewed = useCallback(
    (property: Property): void => {
      if (!property?.id) {
        console.warn('Invalid property provided to addToRecentlyViewed');
        return;
      }

      try {
        console.log(`➕ Adding property to recently viewed: ${property.title}`);
        storageRef.current!.addItem(property);
        reloadFromStorage();
      } catch (err) {
        console.error('Failed to add property to recently viewed:', err);
        setError('Unable to save property to recently viewed');
      }
    },
    [reloadFromStorage]
  );

  // Remove property from recently viewed
  const removeFromRecentlyViewed = useCallback(
    (propertyId: string): void => {
      if (!propertyId) {
        console.warn('Invalid property ID provided to removeFromRecentlyViewed');
        return;
      }

      try {
        console.log(`➖ Removing property from recently viewed: ${propertyId}`);
        storageRef.current!.removeItem(propertyId);
        reloadFromStorage();
      } catch (err) {
        console.error('Failed to remove property from recently viewed:', err);
        setError('Unable to remove property from recently viewed');
      }
    },
    [reloadFromStorage]
  );

  // Clear all recently viewed
  const clearRecentlyViewed = useCallback((): void => {
    try {
      console.log('🗑️ Clearing all recently viewed properties');
      storageRef.current!.clearAll();
      reloadFromStorage();
    } catch (err) {
      console.error('Failed to clear recently viewed properties:', err);
      setError('Unable to clear recently viewed properties');
    }
  }, [reloadFromStorage]);

  // Manual refresh
  const refreshRecentlyViewed = useCallback((): RecentlyViewedStorageItem[] => {
    console.log('🔄 Manually refreshing recently viewed properties');
    return reloadFromStorage();
  }, [reloadFromStorage]);

  // Extract properties with time for display
  const allRecentlyViewedWithTime: RecentlyViewedPropertyWithTime[] = recentlyViewed
    .filter((item): item is RecentlyViewedStorageItem & { property: Property } =>
      Boolean(item.property?.id)
    )
    .map(item => ({
      property: item.property,
      viewedAt: item.viewedAt,
      propertyId: item.propertyId,
      id: item.id,
    }));

  // Extract properties for display (backward compatibility)
  const allRecentlyViewedProperties = allRecentlyViewedWithTime.map(item => item.property);

  // Limited for homepage
  const recentlyViewedProperties = allRecentlyViewedProperties.slice(
    0,
    RECENTLY_VIEWED_CONFIG.displayItems
  );

  // Limited with time for homepage
  const recentlyViewedWithTime = allRecentlyViewedWithTime.slice(
    0,
    RECENTLY_VIEWED_CONFIG.displayItems
  );

  return {
    recentlyViewed,
    recentlyViewedProperties,
    allRecentlyViewedProperties,
    recentlyViewedWithTime,
    allRecentlyViewedWithTime,
    isLoading: isLoading || (enableSync && isLoadingServer),
    error,
    addToRecentlyViewed,
    removeFromRecentlyViewed,
    clearRecentlyViewed,
    refreshRecentlyViewed,
    hasRecentlyViewed: recentlyViewed.length > 0,
    isProcessing: isProcessingRef.current,
    hasSynced: hasSyncedRef.current,
  };
}
