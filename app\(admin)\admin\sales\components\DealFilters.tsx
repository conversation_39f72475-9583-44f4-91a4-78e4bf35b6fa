import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card } from '@/components/ui/card';
import { DealSearchParams } from '@/lib/api/services/fetchDeal';
import { priorityConfig, statusConfig } from '../config/configuration';

interface DealFiltersProps {
  filters: DealSearchParams;
  onFilterChange: (key: keyof DealSearchParams, value: string | boolean) => void;
}

export function DealFilters({ filters, onFilterChange }: DealFiltersProps) {
  return (
    <Card className="w-full p-4">
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search deals..."
              className="pl-8"
              value={filters.searchTerm}
              onChange={e => onFilterChange('searchTerm', e.target.value)}
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Select value={filters.status} onValueChange={value => onFilterChange('status', value)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              {Object.keys(statusConfig).map(status => (
                <SelectItem key={status} value={status}>
                  {status}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={filters.priority}
            onValueChange={value => onFilterChange('priority', value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priorities</SelectItem>
              {Object.keys(priorityConfig.badge).map(priority => (
                <SelectItem key={priority} value={priority}>
                  {priorityConfig.badge[priority as keyof typeof priorityConfig.badge].label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filters.sortBy} onValueChange={value => onFilterChange('sortBy', value)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort By" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="createdAt">Created Date</SelectItem>
              <SelectItem value="updatedAt">Updated Date</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            onClick={() => onFilterChange('isAscending', !filters.isAscending)}
          >
            {filters.isAscending ? 'Ascending' : 'Descending'}
          </Button>
        </div>
      </div>
    </Card>
  );
}
