'use client';

import { useState, useEffect } from 'react';
import { Search, Plus, Folder } from 'lucide-react';
import Image from 'next/image';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useCollections } from '@/hooks/useCollections';
import { Collection } from '@/lib/api/services/fetchCollection';
import CreateCollectionModal from './CreateCollectionModal';

interface AddToCollectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  propertyId: string;
  _propertyTitle: string;
}

interface CollectionThumbnailProps {
  images: string[];
  collectionName: string;
  className?: string;
}

function CollectionThumbnail({ images, collectionName, className = '' }: CollectionThumbnailProps) {
  const placeholderImages = Array(4).fill(null);

  return (
    <div className={`w-12 h-12 rounded-lg overflow-hidden bg-gray-100 ${className}`}>
      {images.length === 0 ? (
        // No images - show folder icon
        <div className="w-full h-full flex items-center justify-center">
          <Folder className="w-5 h-5 text-gray-400" />
        </div>
      ) : images.length === 1 ? (
        // 1 image - show single image
        <div className="w-full h-full relative">
          <Image src={images[0]} alt={collectionName} fill className="object-cover" sizes="48px" />
        </div>
      ) : (
        // Multiple images - show 4-image grid layout
        <div className="w-full h-full grid grid-cols-2 gap-0.5">
          {placeholderImages.map((_, index) => (
            <div key={index} className="relative bg-gray-200">
              {images[index] ? (
                <Image
                  src={images[index]}
                  alt={`${collectionName} - ${index + 1}`}
                  fill
                  className="object-cover"
                  sizes="24px"
                />
              ) : (
                <div className="w-full h-full bg-gray-100" />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

interface CollectionItemProps {
  collection: Collection;
  onSelect: () => void;
  isSelected?: boolean;
  disabled?: boolean;
}

function CollectionItem({ collection, onSelect, isSelected, disabled }: CollectionItemProps) {
  const timeAgo = () => {
    const now = new Date();
    const updated = new Date(collection.updatedAt);
    const diffInMinutes = Math.floor((now.getTime() - updated.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'vừa xong';
    if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} giờ trước`;
    return `${Math.floor(diffInMinutes / 1440)} ngày trước`;
  };

  return (
    <div
      onClick={disabled ? undefined : onSelect}
      className={`
        flex items-center gap-3 sm:gap-4 p-3 sm:p-4 rounded-lg transition-all
        touch-manipulation
        ${
          disabled
            ? 'cursor-not-allowed opacity-50'
            : `cursor-pointer active:scale-95 ${
                isSelected
                  ? 'bg-blue-50 border border-blue-200 shadow-sm'
                  : 'hover:bg-gray-50 border border-transparent hover:shadow-sm'
              }`
        }
      `}
    >
      {/* Collection Thumbnail - Mobile optimized */}
      <CollectionThumbnail
        images={collection.collectionImage || []}
        collectionName={collection.name}
        className="w-12 h-12 sm:w-14 sm:h-14 flex-shrink-0"
      />

      {/* Collection Info - Responsive typography */}
      <div className="flex-1 min-w-0">
        <h4 className="font-medium text-sm sm:text-base text-gray-900 truncate leading-tight">
          {collection.name}
        </h4>
        <div className="flex flex-col xs:flex-row xs:items-center gap-1 xs:gap-2 text-xs sm:text-sm text-gray-500">
          <span className="font-medium">{collection.itemCount} bất động sản</span>
          <span className="hidden xs:inline">•</span>
          <span>cập nhật {timeAgo()}</span>
        </div>
        {disabled && <div className="text-xs text-blue-600 mt-1">Đã có trong bộ sưu tập này</div>}
      </div>
    </div>
  );
}

export default function AddToCollectionModal({
  isOpen,
  onClose,
  propertyId,
  _propertyTitle,
}: AddToCollectionModalProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCollectionId, setSelectedCollectionId] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  const {
    collections,
    isLoading,
    addToCollection,
    isAddingToCollection,
    getCollectionsForProperty,
  } = useCollections();

  // Filter collections based on search query
  const filteredCollections = collections.filter(collection =>
    collection.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Check if property is already in collections
  const existingCollections = getCollectionsForProperty(propertyId);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setSearchQuery('');
      setSelectedCollectionId(null);
    }
  }, [isOpen]);

  const handleAddToCollection = () => {
    if (!selectedCollectionId) return;

    addToCollection(
      { collectionId: selectedCollectionId, propertyId },
      {
        onSuccess: () => {
          onClose();
        },
      }
    );
  };

  const handleCreateNewCollection = () => {
    setShowCreateModal(true);
  };

  const handleCreateSuccess = (newCollectionId: string) => {
    setShowCreateModal(false);
    // Automatically add to the newly created collection
    addToCollection(
      { collectionId: newCollectionId, propertyId },
      {
        onSuccess: () => {
          onClose();
        },
      }
    );
  };

  const handleClose = () => {
    if (!isAddingToCollection) {
      setSearchQuery('');
      setSelectedCollectionId(null);
      onClose();
    }
  };

  // If no collections exist, show create modal immediately
  useEffect(() => {
    if (isOpen && !isLoading && collections.length === 0) {
      setShowCreateModal(true);
    }
  }, [isOpen, isLoading, collections.length]);

  return (
    <>
      <Dialog open={isOpen && !showCreateModal} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] max-w-sm sm:max-w-md p-0 gap-0 bg-white mx-auto">
          {/* Header - Mobile optimized */}
          <DialogHeader className="px-4 sm:px-6 py-4 sm:py-5 border-b border-gray-100">
            <div className="pr-6 sm:pr-0">
              <DialogTitle className="text-base sm:text-lg font-medium text-gray-900 leading-tight">
                Thêm bất động sản vào bộ sưu tập
              </DialogTitle>
            </div>
          </DialogHeader>

          {/* Content - Responsive spacing */}
          <div className="px-4 sm:px-6 py-4 sm:py-6 space-y-4 sm:space-y-5">
            {/* Search Input - Mobile-friendly */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Tìm kiếm bộ sưu tập"
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="pl-10 h-11 sm:h-10 text-base sm:text-sm border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                disabled={isAddingToCollection}
              />
            </div>

            {/* Collections List - Mobile-optimized scrolling */}
            {isLoading ? (
              <div className="flex items-center justify-center py-12 sm:py-8">
                <div className="w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
              </div>
            ) : (
              <div className="space-y-2 max-h-64 sm:max-h-72 overflow-y-auto">
                {filteredCollections.length > 0 ? (
                  filteredCollections.map(collection => {
                    const isAlreadyInCollection = existingCollections.some(
                      ec => ec.id === collection.id
                    );

                    return (
                      <CollectionItem
                        key={collection.id}
                        collection={collection}
                        onSelect={() => {
                          if (!isAlreadyInCollection) {
                            setSelectedCollectionId(collection.id);
                          }
                        }}
                        isSelected={selectedCollectionId === collection.id}
                        disabled={isAlreadyInCollection}
                      />
                    );
                  })
                ) : (
                  <div className="text-center py-12 sm:py-8 text-gray-500">
                    <div className="text-sm sm:text-base">
                      {searchQuery ? 'Không tìm thấy bộ sưu tập phù hợp' : 'Chưa có bộ sưu tập nào'}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Create New Collection Button - Mobile-friendly */}
            <Button
              variant="ghost"
              onClick={handleCreateNewCollection}
              disabled={isAddingToCollection}
              className="w-full justify-start h-11 sm:h-10 text-gray-600 hover:text-gray-800 hover:bg-gray-50 font-medium"
            >
              <Plus className="w-4 h-4 mr-2" />
              <span className="text-sm sm:text-base">Tạo bộ sưu tập mới</span>
            </Button>
          </div>

          {/* Action Buttons - Mobile-optimized */}
          <div className="px-4 sm:px-6 py-4 sm:py-5 border-t border-gray-100">
            <div className="flex flex-col-reverse sm:flex-row gap-3 sm:gap-0 sm:justify-end">
              <Button
                variant="ghost"
                onClick={handleClose}
                disabled={isAddingToCollection}
                className="w-full sm:w-auto h-11 sm:h-auto sm:px-4 sm:py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 font-medium"
              >
                Hủy
              </Button>
              <Button
                onClick={handleAddToCollection}
                disabled={!selectedCollectionId || isAddingToCollection}
                className="w-full sm:w-auto h-11 sm:h-auto sm:px-6 sm:py-2 sm:ml-3 bg-gray-900 hover:bg-gray-800 text-white disabled:bg-gray-300 disabled:cursor-not-allowed font-medium"
              >
                {isAddingToCollection ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Đang thêm...</span>
                  </div>
                ) : (
                  'Hoàn thành'
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Create Collection Modal */}
      <CreateCollectionModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={handleCreateSuccess}
      />
    </>
  );
}
