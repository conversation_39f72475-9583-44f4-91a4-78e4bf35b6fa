import { propertyService, PropertyStatus } from '@/lib/api/services/fetchProperty';

export default async function sitemap() {
  const baseUrl = 'https://www.revoland.vn';

  // Add static pages
  const routes = [
    '',
    '/',
    '/properties',
    // '/vi',
    // '/en',
    // '/vi/tin-tuc',
    // '/en/tin-tuc',
    // Add other static routes
  ].map(route => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date().toISOString(),
    changeFrequency: 'daily',
    priority: 1,
  }));

  // Add property pages
  let propertyRoutes: Array<{
    url: string;
    lastModified: string;
    changeFrequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
    priority: number;
  }> = [];

  try {
    const propertiesResponse = await propertyService.getProperties({
      pageSize: 1000, // Get all properties for sitemap
      status: PropertyStatus.AVAILABLE,
    });

    if (propertiesResponse.data?.properties) {
      propertyRoutes = propertiesResponse.data.properties.map(property => ({
        url: `${baseUrl}/properties/${property.id}`,
        lastModified: property.updatedAt,
        changeFrequency: 'weekly' as const,
        priority: 0.8,
      }));
    }
  } catch (error) {
    console.error('Error fetching properties for sitemap:', error);
  }

  return [...routes, ...propertyRoutes];
}
