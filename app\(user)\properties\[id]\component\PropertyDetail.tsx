'use client';

import { Property } from '@/lib/api/services/fetchProperty';
import PropertyShowcase from './PropertiesShowcase';
import PropertyDetailsSection from './PropertyDetailsSection';
import TopPropertiesSection from './TopPropertiesSection';
import PropertyStructuredData from './PropertyStructuredData';
import StickyTabs from './StickyTabs';

type PropertyDetailProps = {
  property: Property;
};

export default function PropertyDetail({ property }: PropertyDetailProps) {
  return (
    <>
      <PropertyStructuredData property={property} />
      <StickyTabs property={property} />
      <div className="min-h-screen bg-background text-foreground">
        <div className="w-full max-w-screen px-4 md:px-8 py-4 mx-auto bg-background text-foreground">
          <PropertyShowcase property={property} />
          <div className="xl:px-32">
            <PropertyDetailsSection property={property} />
            <TopPropertiesSection currentProperty={property} />
            {/* <PropertyPriceDetails property={property} />
            <ContactForm property={property} /> */}
          </div>
        </div>
      </div>
    </>
  );
}
