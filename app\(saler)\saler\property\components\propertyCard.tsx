import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  MapPin,
  Bed,
  Bath,
  Home,
  Building,
  ChevronLeft,
  ChevronRight,
  Trash,
  Share2,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Property,
  PropertyStatus,
  PropertyType,
  TransactionType,
} from '@/lib/api/services/fetchProperty';
import { cn } from '@/lib/utils';

import { PropertyPreviewDialog } from '@/components/property-preview-dialog';

interface PropertyCardProps {
  property: Property;
  viewMode?: 'grid' | 'list';
  onDelete: () => void;
}

const getStatusColor = (status: PropertyStatus) => {
  switch (status) {
    case PropertyStatus.AVAILABLE:
      return 'bg-green-100 text-green-800 border-green-200';
    case PropertyStatus.PENDING:
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case PropertyStatus.SOLD:
      return 'bg-red-100 text-red-800 border-red-200';
    case PropertyStatus.RENTED:
      return 'bg-blue-100 text-blue-800 border-blue-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getPropertyTypeText = (type: PropertyType, transactionType: TransactionType) => {
  const typeMap = {
    [PropertyType.APARTMENT]: 'Chung cư',
    [PropertyType.LAND_PLOT]: 'Nhà Đất',
    [PropertyType.VILLA]: 'Biệt thự',
    [PropertyType.SHOP_HOUSE]: 'Nhà phố',
    [PropertyType.MINI_SERVICE_APARTMENT]: 'Chung cư mini',
    [PropertyType.MOTEL]: 'Nhà nghỉ',
    [PropertyType.AIRBNB]: 'Nhà nghỉ',
    [PropertyType.HOUSE]: 'Nhà riêng',
    [PropertyType.TOWNHOUSE]: 'Nhà liền kề',
    [PropertyType.PROJECT_LAND]: 'Đất',
    [PropertyType.OFFICE]: 'Văn phòng',
    [PropertyType.WAREHOUSE]: 'Kho',
    [PropertyType.FACTORY]: 'Nhà máy',
    [PropertyType.INDUSTRIAL]: 'Nhà máy',
    [PropertyType.HOTEL]: 'Khách sạn',
    [PropertyType.SOCIAL_HOUSING]: 'Nhà ở xã hội',
    [PropertyType.NEW_URBAN_AREA]: 'Khu đô thị mới',
    [PropertyType.ECO_RESORT]: 'Khu resort',
    [PropertyType.OTHER]: 'Khác',
  };

  const transactionText = transactionType === TransactionType.FOR_SALE ? 'bán' : 'cho thuê';
  return `${typeMap[type as keyof typeof typeMap]} ${transactionText}`;
};

const getPropertyType = (type: PropertyType) => {
  const typeMap = {
    [PropertyType.APARTMENT]: 'Chung cư',
    [PropertyType.LAND_PLOT]: 'Nhà Đất',
    [PropertyType.VILLA]: 'Biệt thự',
    [PropertyType.SHOP_HOUSE]: 'Nhà phố',
    [PropertyType.MINI_SERVICE_APARTMENT]: 'Chung cư mini',
    [PropertyType.MOTEL]: 'Nhà nghỉ',
    [PropertyType.AIRBNB]: 'Nhà nghỉ',
    [PropertyType.HOUSE]: 'Nhà riêng',
    [PropertyType.TOWNHOUSE]: 'Nhà liền kề',
    [PropertyType.PROJECT_LAND]: 'Đất',
    [PropertyType.OFFICE]: 'Văn phòng',
    [PropertyType.WAREHOUSE]: 'Kho',
    [PropertyType.FACTORY]: 'Nhà máy',
    [PropertyType.INDUSTRIAL]: 'Nhà máy',
    [PropertyType.HOTEL]: 'Khách sạn',
    [PropertyType.SOCIAL_HOUSING]: 'Nhà ở xã hội',
    [PropertyType.NEW_URBAN_AREA]: 'Khu đô thị mới',
    [PropertyType.ECO_RESORT]: 'Khu resort',
    [PropertyType.OTHER]: 'Khác',
  };

  return `${typeMap[type as keyof typeof typeMap]}`;
};

const formatPrice = (price: number, transactionType: TransactionType) => {
  if (transactionType === TransactionType.FOR_RENT) {
    if (price >= 1000000) {
      return (
        <span>
          {(price / 1000000).toFixed(1)} triệu
          <span className="text-sm text-muted-foreground font-light"> /tháng</span>
        </span>
      );
    }
    return (
      <span>
        {(price / 1000).toFixed(1)} nghìn
        <span className="text-sm text-muted-foreground font-light"> /tháng</span>
      </span>
    );
  } else {
    if (price >= 1000000000) {
      return <span>{(price / 1000000000).toFixed(1)} tỷ</span>;
    } else if (price >= 1000000) {
      return <span>{(price / 1000000).toFixed(1)} triệu</span>;
    } else {
      return <span>{(price / 1000).toFixed(1)} nghìn</span>;
    }
  }
};

const getStatusLabel = (status: PropertyStatus) => {
  switch (status) {
    case PropertyStatus.AVAILABLE:
      return 'Có sẵn';
    case PropertyStatus.PENDING:
      return 'Đang chờ';
    case PropertyStatus.SOLD:
      return 'Đã bán';
    case PropertyStatus.RENTED:
      return 'Đã cho thuê';
    default:
      return status;
  }
};

export function PropertyCard({ property, viewMode = 'grid', onDelete }: PropertyCardProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [preloadedImages, setPreloadedImages] = useState<number[]>([0]);
  const [isImageLoading, setIsImageLoading] = useState(true);
  const [showPreview, setShowPreview] = useState(false);

  // Handle hover state changes
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  // Preload next and previous images
  React.useEffect(() => {
    const preloadImage = (index: number) => {
      if (index >= 0 && index < property.imageUrls.length && !preloadedImages.includes(index)) {
        const img = new window.Image();
        img.src = property.imageUrls[index];
        setPreloadedImages(prev => [...prev, index]);
      }
    };

    // Preload next image
    const nextIndex = (currentImageIndex + 1) % property.imageUrls.length;
    preloadImage(nextIndex);

    // Preload previous image
    const prevIndex =
      currentImageIndex === 0 ? property.imageUrls.length - 1 : currentImageIndex - 1;
    preloadImage(prevIndex);
  }, [currentImageIndex, property.imageUrls, preloadedImages]);

  const nextImage = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsImageLoading(true);
    setCurrentImageIndex(prev => (prev === property.imageUrls.length - 1 ? 0 : prev + 1));
  };

  const prevImage = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsImageLoading(true);
    setCurrentImageIndex(prev => (prev === 0 ? property.imageUrls.length - 1 : prev - 1));
  };

  if (viewMode === 'list') {
    return (
      <Card className="group hover:shadow-md transition-all duration-300 shadow-sm bg-background border rounded-2xl overflow-hidden font-mann">
        <div className="grid grid-cols-1 sm:grid-cols-3 h-auto sm:h-40 md:h-48 lg:h-52">
          <div className="relative col-span-1 sm:col-span-1 h-52 sm:h-full flex-shrink-0 md:h-48 lg:h-52">
            <div className="relative w-full h-full overflow-hidden rounded-t-lg sm:rounded-l-lg sm:rounded-tr-none">
              {property.imageUrls.length > 0 ? (
                <>
                  {isImageLoading && <div className="absolute inset-0 bg-gray-200 animate-pulse" />}
                  <Image
                    src={property.imageUrls[currentImageIndex] || '/placeholder-property.jpg'}
                    alt={property.title}
                    fill
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    className={cn(
                      'object-cover transition-opacity duration-300',
                      isImageLoading ? 'opacity-0' : 'opacity-100'
                    )}
                    onLoad={() => setIsImageLoading(false)}
                  />
                </>
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <Building className="h-8 w-8 text-gray-400" />
                </div>
              )}

              <div className="absolute top-2 left-2 flex gap-2">
                <Badge
                  variant="outline"
                  className="bg-white/90 backdrop-blur-sm hover:bg-white/90 text-xs"
                >
                  {property.transactionType === TransactionType.FOR_SALE ? 'Bán' : 'Cho thuê'}
                </Badge>
                <Badge
                  variant="outline"
                  className="bg-white/90 backdrop-blur-sm hover:bg-white/90 text-xs"
                >
                  {getPropertyType(property.type)}
                </Badge>
              </div>

              <div className="absolute top-2 right-2">
                <Badge className={cn('border text-xs', getStatusColor(property.status))}>
                  {getStatusLabel(property.status)}
                </Badge>
              </div>
            </div>
          </div>

          <CardContent className="col-span-1 sm:col-span-2 p-3 flex flex-col justify-between">
            <div className="space-y-2">
              <div className="text-lg sm:text-xl font-bold text-foreground">
                {formatPrice(
                  property.transactionType === TransactionType.FOR_SALE
                    ? property.priceDetails.salePrice || 0
                    : property.priceDetails.rentalPrice || 0,
                  property.transactionType
                )}
              </div>

              <div className="flex items-start gap-1">
                <MapPin className="h-3 w-3 text-foreground mt-0.5 flex-shrink-0" />
                <p className="text-xs sm:text-sm text-foreground line-clamp-2 sm:line-clamp-1">
                  {property.location?.address || 'Location unavailable'},{' '}
                  {property.location?.district}
                </p>
              </div>
            </div>

            <div className="flex items-center justify-start gap-10">
              <div className="flex items-center gap-1">
                <div className="text-sm sm:text-base font-semibold text-foreground">
                  {property.propertyDetails.buildingArea || '-'}
                </div>
                <div className="text-xs text-muted-foreground truncate">m²</div>
              </div>
              <div className="flex items-center gap-1">
                <div className="text-sm sm:text-base font-semibold text-foreground">
                  {property.propertyDetails.landArea || '-'}
                </div>
                <div className="text-xs text-muted-foreground truncate">Diện tích đất</div>
              </div>
              <div className="flex items-center gap-1">
                <div className="text-sm sm:text-base font-semibold text-foreground">
                  {property.propertyDetails.numberOfFloors || '-'}
                </div>
                <div className="text-xs text-muted-foreground truncate">Số tầng</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-2">
              <div className="flex items-center gap-3 text-xs">
                <div className="flex items-center gap-1">
                  <Home className="h-3 w-3 text-foreground" />
                  <span>{property.propertyDetails.livingRooms || 0}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Bed className="h-3 w-3 text-foreground" />
                  <span>{property.propertyDetails.bedrooms || 0}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Bath className="h-3 w-3 text-foreground" />
                  <span>{property.propertyDetails.bathrooms || 0}</span>
                </div>
                <span className="text-muted-foreground">
                  {property.propertyDetails.buildingArea || '-'} m²
                </span>
              </div>

              <div className="flex gap-2 sm:gap-1">
                <Link
                  href={`/saler/property/action?id=${property.id}`}
                  className="flex-1 sm:flex-none"
                >
                  <Button variant="outline" size="sm" className="w-full sm:w-auto h-7 px-3 text-xs">
                    Sửa
                  </Button>
                </Link>

                <Button
                  size="sm"
                  className="flex-1 sm:flex-none w-full sm:w-auto h-7 px-3 text-xs"
                  onClick={() => setShowPreview(true)}
                >
                  Xem
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  className="w-full sm:w-auto h-7 px-3 text-xs bg-red-500 hover:bg-red-600"
                  onClick={onDelete}
                >
                  <Trash className="h-3 w-3 text-white" />
                </Button>
              </div>
            </div>
          </CardContent>
        </div>

        <PropertyPreviewDialog
          property={property}
          open={showPreview}
          onOpenChange={setShowPreview}
        />
      </Card>
    );
  }

  return (
    <div
      className="overflow-hidden transition-all duration-300 flex flex-col font-mann"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Image Section with Badge */}
      <div className="relative aspect-square size-full mb-2 group">
        {/* Skeleton Loading */}
        {isImageLoading && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-xl" />
        )}

        {/* Main Image */}
        <Image
          src={property.imageUrls[currentImageIndex] || '/placeholder-property.jpg'}
          alt={property.title}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className={cn(
            'object-cover rounded-2xl transition-opacity duration-300',
            isImageLoading ? 'opacity-0' : 'opacity-100'
          )}
          onLoad={() => setIsImageLoading(false)}
        />

        {/* Preload Hidden Images */}
        {property.imageUrls.map(
          (url, index) =>
            index !== currentImageIndex && (
              <Image
                key={index}
                src={url}
                alt={`${property.title} - Image ${index + 1}`}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                className="hidden"
                priority={index === 0}
              />
            )
        )}

        <div className="absolute top-4 left-4 flex gap-2">
          <Badge variant="outline" className="bg-white/90 backdrop-blur-sm hover:bg-white/90 z-10">
            {property.transactionType === TransactionType.FOR_SALE ? 'Bán' : 'Cho thuê'}
          </Badge>
          <Badge variant="outline" className="bg-white/90 backdrop-blur-sm hover:bg-white/90 z-10">
            {getPropertyType(property.type)}
          </Badge>
        </div>

        {/* Status Badge */}
        <div className="absolute top-4 right-4">
          <Badge className={cn('border text-xs', getStatusColor(property.status))}>
            {getStatusLabel(property.status)}
          </Badge>
        </div>

        {/* Navigation Buttons */}
        {isHovered && property.imageUrls.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white/90 rounded-full h-8 w-8 z-10"
              onClick={prevImage}
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white/90 rounded-full h-8 w-8 z-10"
              onClick={nextImage}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </>
        )}

        {/* Image Counter */}
        {property.imageUrls.length > 1 && (
          <div className="absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded-full z-10">
            {currentImageIndex + 1}/{property.imageUrls.length}
          </div>
        )}
      </div>

      <div className="px-1">
        {/* Price and Actions Section */}
        <div className="flex justify-between items-center">
          <div>
            <p className="text-xl font-semibold">
              {formatPrice(
                property.transactionType === TransactionType.FOR_SALE
                  ? property.priceDetails.salePrice || 0
                  : property.priceDetails.rentalPrice || 0,
                property.transactionType
              )}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="icon"
              className="hover:text-blue-600 transition-colors"
              onClick={() => setShowPreview(true)}
            >
              <Share2 className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="hover:text-red-600 transition-colors"
              onClick={onDelete}
            >
              <Trash className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* Property Details Section */}
        <div className="flex justify-between items-center mb-2 text-foreground text-sm">
          <div className="flex gap-4">
            <div className="flex items-center">
              <Bed className="size-4 mr-1" />
              <span>{property.propertyDetails.bedrooms}</span>
            </div>
            <div className="flex items-center">
              <Bath className="size-4 mr-1" />
              <span>{property.propertyDetails.bathrooms}</span>
            </div>
            <div className="flex items-center">
              <Home className="size-4 mr-1" />
              <span>{property.propertyDetails.buildingArea} m²</span>
            </div>
          </div>
          <div className="text-xs text-muted-foreground">
            {getPropertyTypeText(property.type, property.transactionType)}
          </div>
        </div>

        {/* Location Section */}
        <div className="mb-2">
          <div className="flex items-center text-xs text-muted-foreground">
            <MapPin className="size-4 mr-1" />
            <span>
              {property.location?.city || 'Location unavailable'}, {property.location?.district}
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 mt-3">
          <Link href={`/saler/property/action?id=${property.id}`} className="flex-1">
            <Button variant="outline" className="w-full h-8 text-xs">
              Sửa
            </Button>
          </Link>
          <Button className="flex-1 w-full h-8 text-xs" onClick={() => setShowPreview(true)}>
            Xem
          </Button>
        </div>
      </div>

      <PropertyPreviewDialog property={property} open={showPreview} onOpenChange={setShowPreview} />
    </div>
  );
}
