import { useMutation, useQuery } from '@tanstack/react-query';
import { recentlyViewedService } from '@/lib/api/services/fetchRecentlyViewed';

/**
 * Hook for syncing recently viewed property IDs to server
 *
 * @example
 * const syncMutation = useSyncRecentlyViewed();
 * await syncMutation.mutateAsync(['property1', 'property2']);
 */
export function useSyncRecentlyViewed() {
  return useMutation({
    mutationFn: (propertyIds: string[]) => recentlyViewedService.syncRecentlyViewed(propertyIds),
    meta: {
      description: 'Sync recently viewed property IDs to server',
    },
  });
}

/**
 * Hook for fetching recently viewed property IDs from server
 *
 * @param enabled - Whether the query should be enabled (default: false)
 * @returns Query with server property IDs
 *
 * @example
 * const { data: serverPropertyIds, isLoading } = useFetchRecentlyViewed(true);
 */
export function useFetchRecentlyViewed(enabled = false) {
  return useQuery({
    queryKey: ['recently-viewed-server'],
    queryFn: () => recentlyViewedService.fetchRecentlyViewed(),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (garbage collection time)
    retry: 2,
    meta: {
      description: 'Fetch recently viewed property IDs from server',
    },
  });
}
