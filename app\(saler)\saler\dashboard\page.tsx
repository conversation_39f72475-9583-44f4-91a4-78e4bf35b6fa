'use client';

import { ChartAreaInteractive } from '@/components/chartAreaInteractive';
import { DataTable, columns } from '@/components/dataTable';
import { SectionCards } from '@/components/sectionCards';
import { SiteHeader } from '@/components/common/siteHeader';

import data from './data.json';

export default function Page() {
  return (
    <div className="flex flex-col h-full">
      <SiteHeader title="Dashboard" />
      <div className="flex-1 overflow-auto scrollbar-hide">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <SectionCards />
            <div className="px-4 lg:px-6">
              <ChartAreaInteractive />
            </div>
            <DataTable data={data} columns={columns} />
          </div>
        </div>
      </div>
    </div>
  );
}
