import apiService from '@/lib/api/core';
import { Property } from './fetchProperty';

export interface Collection {
  id: string;
  name: string;
  description?: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  itemCount: number;
  thumbnailUrl?: string;
  collectionImage?: string[];
  // Add lazy loading flag
  isImageLoaded?: boolean;
}

export interface CollectionItem {
  id: string;
  collectionId: string;
  propertyId: string;
  addedAt: string;
}

export interface CollectionWithItems extends Collection {
  items: CollectionItem[];
  properties?: Property[];
}

export interface ApiCollectionData {
  collectionId: string;
  collectionName: string;
  description: string;
  userId: string;
  listProperties: Property[];
  collectionImage: string[];
}

export interface CreateCollectionRequest {
  name: string;
  description?: string;
}

export interface AddToCollectionRequest {
  collectionId: string;
  propertyId: string;
}

export interface RemoveFromCollectionRequest {
  collectionId: string;
  propertyId: string;
}

export interface UpdateCollectionRequest {
  name: string;
  description: string;
}

export interface CollectionResponse {
  code: number;
  status: boolean;
  message: string;
  data: Collection | null;
}

export interface CollectionsResponse {
  code: number;
  status: boolean;
  message: string;
  data: Collection[];
}

export interface ApiCollectionsResponse {
  code: number;
  status: boolean;
  message: string;
  data: ApiCollectionData[];
}

export interface ActionResponse {
  code: number;
  status: boolean;
  message: string;
  data?: unknown;
}

// Extract valid images from properties
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const extractValidImages = (properties: any[], maxImages: number = 4): string[] => {
  const validImages: string[] = [];

  for (const property of properties.slice(0, maxImages)) {
    if (validImages.length >= maxImages) break;

    const propertyImages = property.images || property.imageUrls || [];
    if (propertyImages?.length > 0) {
      const firstImage = propertyImages[0];
      if (
        firstImage &&
        firstImage.trim() !== '' &&
        !firstImage.includes('undefined') &&
        firstImage.includes('storage.googleapis.com')
      ) {
        validImages.push(firstImage);
      }
    }
  }

  return validImages;
};

// Extract collection images with validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const extractCollectionImages = (properties: any[], existingImages: string[] = []): string[] => {
  // Validate existing images first
  const validExistingImages = (existingImages || [])
    .filter(
      img =>
        img &&
        img.trim() !== '' &&
        !img.includes('undefined') &&
        img.includes('storage.googleapis.com')
    )
    .slice(0, 4);

  if (validExistingImages.length > 0) {
    return validExistingImages;
  }

  // Extract from properties if no existing images
  return extractValidImages(properties || [], 4);
};

// Transform API data to Collection format
const transformApiCollectionToCollection = (apiData: ApiCollectionData): Collection => {
  const collectionImages = extractCollectionImages(apiData.listProperties, apiData.collectionImage);

  return {
    id: apiData.collectionId,
    name: apiData.collectionName,
    description: apiData.description || undefined,
    userId: apiData.userId,
    itemCount: apiData.listProperties?.length || 0,
    thumbnailUrl: collectionImages[0] || undefined,
    collectionImage: collectionImages,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isImageLoaded: false,
  };
};

// Request debouncing to prevent rapid API calls
let lastRequestTime = 0;
const MIN_REQUEST_INTERVAL = 200;

const debounceRequest = async <T>(requestFn: () => Promise<T>): Promise<T> => {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    await new Promise(resolve => setTimeout(resolve, MIN_REQUEST_INTERVAL - timeSinceLastRequest));
  }

  lastRequestTime = Date.now();
  return requestFn();
};

// Collection Service
export const collectionService = {
  // GET: Get all collections
  getAllByUser: async (): Promise<CollectionsResponse> => {
    try {
      const response = await debounceRequest(async () => {
        return apiService.get<ApiCollectionsResponse>('/api/CollectionProperty/GetAllByUser');
      });

      if (!response.data.status) {
        return {
          code: response.data.code,
          status: false,
          message: response.data.message || 'Không thể tải bộ sưu tập',
          data: [],
        };
      }

      const collections = (response.data.data || [])
        .slice(0, 10)
        .map(transformApiCollectionToCollection);

      return {
        code: response.data.code,
        status: response.data.status,
        message: response.data.message,
        data: collections,
      };
    } catch (error) {
      return {
        code: 500,
        status: false,
        message: 'Có lỗi xảy ra khi tải bộ sưu tập',
        data: [],
      };
    }
  },

  // POST: Tạo collection mới
  createCollection: async (request: CreateCollectionRequest): Promise<CollectionResponse> => {
    try {
      const response = await debounceRequest(async () => {
        return apiService.post<ActionResponse>(
          '/api/CollectionProperty',
          request as unknown as Record<string, unknown>
        );
      });

      return {
        code: response.data.code,
        status: response.data.status,
        message: response.data.message,
        data: null,
      };
    } catch (error) {
      return {
        code: 500,
        status: false,
        message: 'Có lỗi xảy ra khi tạo bộ sưu tập',
        data: null,
      };
    }
  },

  // POST: Thêm property vào collection
  addPropertyToCollection: async (request: AddToCollectionRequest): Promise<ActionResponse> => {
    try {
      const { collectionId, propertyId } = request;
      const response = await debounceRequest(async () => {
        return apiService.post<ActionResponse>(
          `/api/CollectionProperty/add-property?collectionId=${collectionId}&propertyId=${propertyId}`
        );
      });

      return response.data;
    } catch (error) {
      return {
        code: 500,
        status: false,
        message: 'Có lỗi xảy ra khi thêm vào bộ sưu tập',
      };
    }
  },

  // DELETE: Xóa property khỏi collection
  removePropertyFromCollection: async (
    request: RemoveFromCollectionRequest
  ): Promise<ActionResponse> => {
    try {
      const { collectionId, propertyId } = request;
      const response = await debounceRequest(async () => {
        return apiService.delete<ActionResponse>(
          `/api/CollectionProperty/remove-property?collectionId=${collectionId}&property=${propertyId}`
        );
      });

      return response.data;
    } catch (error) {
      return {
        code: 500,
        status: false,
        message: 'Có lỗi xảy ra khi xóa khỏi bộ sưu tập',
      };
    }
  },

  // DELETE: Xóa collection
  deleteCollection: async (collectionId: string): Promise<ActionResponse> => {
    try {
      const response = await debounceRequest(async () => {
        return apiService.delete<ActionResponse>(`/api/CollectionProperty/${collectionId}`);
      });

      return response.data;
    } catch (error) {
      return {
        code: 500,
        status: false,
        message: 'Có lỗi xảy ra khi xóa bộ sưu tập',
      };
    }
  },

  // PUT: Cập nhật collection
  updateCollection: async (
    collectionId: string,
    request: UpdateCollectionRequest
  ): Promise<ActionResponse> => {
    try {
      const response = await debounceRequest(async () => {
        return apiService.put<ActionResponse>(
          `/api/CollectionProperty/${collectionId}`,
          request as unknown as Record<string, unknown>
        );
      });

      return response.data;
    } catch (error) {
      return {
        code: 500,
        status: false,
        message: 'Có lỗi xảy ra khi cập nhật bộ sưu tập',
      };
    }
  },

  // GET: Lấy chi tiết collection với lazy loading
  getCollectionWithProperties: async (
    collectionId: string
  ): Promise<CollectionWithItems | null> => {
    try {
      const response = await debounceRequest(async () => {
        return apiService.get<ApiCollectionsResponse>('/api/CollectionProperty/GetAllByUser');
      });

      if (!response.data.status) {
        return null;
      }

      const apiCollection = response.data.data?.find(col => col.collectionId === collectionId);
      if (!apiCollection) return null;

      const collection = transformApiCollectionToCollection(apiCollection);

      return {
        ...collection,
        items: (apiCollection.listProperties || []).map(prop => ({
          id: `${collectionId}-${prop.id}`,
          collectionId,
          propertyId: prop.id,
          addedAt: new Date().toISOString(),
        })),
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        properties: (apiCollection.listProperties || []).map((prop: any) => ({
          ...prop,
          imageUrls: prop.images || prop.imageUrls || [], // Use images from API, fallback to imageUrls
        })),
      };
    } catch (error) {
      return null;
    }
  },
};
