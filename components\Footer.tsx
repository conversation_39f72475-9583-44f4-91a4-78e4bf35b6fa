'use client';

import Link from 'next/link';
import { Send } from 'lucide-react';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

export default function Footer() {
  return (
    <footer className="w-full bg-white text-zinc-900">
      <div className="container mx-auto px-4 py-16">
        <div className="grid gap-12 md:grid-cols-6">
          <div className="space-y-8 md:col-span-2">
            <Link href="/" className="flex items-center">
              <Image
                src="/logo_revoland_red.png"
                alt="Revoland icon"
                width={40}
                height={40}
                priority
                className="rounded-md"
              />
              <span className="lg:block hidden text-2xl font-medium text-red-500">Revoland</span>
            </Link>
            <p className="text-zinc-600 text-sm leading-relaxed">
              <PERSON>h<PERSON><PERSON> ph<PERSON> bất động sản mơ ước của bạn với Revoland. Chúng tôi kết nối bạn với cơ hội
              bất động sản hoàn hảo.
            </p>
            <div className="flex">
              <Input
                type="email"
                placeholder="Email"
                className="rounded-r-none bg-zinc-50 border-zinc-200 text-zinc-900 placeholder:text-zinc-400 focus:ring-1 focus:ring-zinc-300"
              />
              <Button
                variant="ghost"
                size="icon"
                className="rounded-l-none border border-l-0 border-zinc-200 bg-zinc-50 hover:bg-zinc-100 text-zinc-900"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div className="md:col-span-4 grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
            {[
              {
                title: 'Trang chủ',
                links: ['Trang chủ', 'Tính năng', 'Bất động sản', 'Bài viết', 'Câu hỏi thường gặp'],
              },
              {
                title: 'Về chúng tôi',
                links: ['Truyền thông', 'Công ty', 'Cách làm việc', 'Đội ngũ', 'Khách hàng'],
              },
              {
                title: 'Dịch vụ',
                links: ['Định giá', 'Marketing', 'Đàm phán', 'Đóng cửa', 'Quản lý bất động sản'],
              },
              {
                title: 'Liên hệ',
                links: ['Liên hệ', 'Văn phòng'],
              },
            ].map(({ title, links }) => (
              <div key={title}>
                <h3 className="mb-4 text-base font-semibold text-zinc-900">{title}</h3>
                <ul className="space-y-3">
                  {links.map(key => (
                    <li key={key}>
                      <Link
                        href="#"
                        className="text-sm text-zinc-600 hover:text-zinc-900 transition-colors duration-200"
                      >
                        {key}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-zinc-200">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
            <p className="text-sm text-zinc-600">
              © {new Date().getFullYear()} Bản quyền thuộc về Revoland
            </p>
            <div className="flex items-center gap-6">
              <Link
                href="#"
                className="text-sm text-zinc-600 hover:text-zinc-900 transition-colors duration-200"
              >
                Điều khoản và điều kiện
              </Link>
              <Link
                href="#"
                className="text-sm text-zinc-600 hover:text-zinc-900 transition-colors duration-200"
              >
                Chính sách bảo mật
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
