'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import Link from 'next/link';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/hooks/useAuth';
import { useAuthStore } from '@/lib/store/authStore';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';

const forgotPasswordSchema = z.object({
  email: z.string().min(1, 'Email là bắt buộc').email('Email không hợp lệ'),
});

export function ForgotPasswordForm() {
  const { forgotPassword, isLoading } = useAuth();
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(forgotPasswordSchema),
    mode: 'onChange',
  });

  const onSubmit = async (data: z.infer<typeof forgotPasswordSchema>) => {
    try {
      await forgotPassword(data);
    } catch (error) {
      console.error('Forgot password error:', error);
      toast.error('Đã có lỗi xảy ra khi gửi yêu cầu. Vui lòng thử lại sau.');
    }
  };

  if (isAuthenticated) return null;

  return (
    <div className="flex flex-col items-center justify-center font-mann">
      <div className="w-full space-y-6 rounded-xl">
        <div className="flex flex-col items-center gap-2 text-center">
          <h1 className="text-xl md:text-2xl font-bold tracking-tight text-red-500">
            Quên mật khẩu
          </h1>
          <p className="text-balance text-xs md:text-sm text-muted-foreground">
            Nhập email để nhận liên kết đặt lại mật khẩu
          </p>
        </div>

        <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-xs md:text-sm">
                Địa chỉ email
                <span className="text-destructive"> *</span>
              </Label>
              <Input
                id="email"
                {...register('email')}
                type="email"
                placeholder="Nhập địa chỉ email của bạn"
                className={cn(errors.email && 'border-destructive text-xs md:text-sm')}
                aria-describedby="email-error"
              />
              {errors.email && (
                <p className="text-xs text-destructive" id="email-error">
                  {errors.email.message}
                </p>
              )}
            </div>
          </div>

          <Button
            type="submit"
            className="w-full bg-red-500 hover:bg-red-600"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isLoading ? 'Đang gửi yêu cầu...' : 'Gửi liên kết đặt lại mật khẩu'}
              </>
            ) : (
              'Gửi liên kết đặt lại mật khẩu'
            )}
          </Button>
        </form>

        <div className="text-center">
          <p className="text-xs md:text-sm text-muted-foreground">
            Nhớ mật khẩu của bạn?{' '}
            <Link href="/login" className="text-primary hover:underline">
              Đăng nhập
            </Link>
          </p>
        </div>

        <div className="text-balance text-center text-xs md:text-sm text-muted-foreground">
          Bằng cách tiếp tục, bạn đồng ý với{' '}
          <Link href="/terms" className="hover:text-primary hover:underline">
            Điều khoản, điều kiện
          </Link>{' '}
          và{' '}
          <Link href="/privacy" className="hover:text-primary hover:underline">
            Chính sách bảo mật
          </Link>
          .
        </div>
      </div>
    </div>
  );
}
