'use client';
import { Property } from '@/lib/api/services/fetchProperty';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { PropertyCard } from '@/components/PropertyCard';
import { motion, AnimatePresence } from 'framer-motion';

interface ModalPropertyProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  properties: Property[];
  onSelectProperty: (property: Property) => void;
}

export default function ModalProperty({
  open,
  onOpenChange,
  properties,
  onSelectProperty,
}: ModalPropertyProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl font-mann">
        <DialogHeader>
          <DialogTitle>Chọn bất động sản để so sánh</DialogTitle>
        </DialogHeader>

        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[60vh] overflow-y-auto"
          >
            {properties.length === 0 && (
              <div className="col-span-2 text-center text-gray-500">
                Không còn bất động sản nào để thêm.
              </div>
            )}

            {properties.map(property => (
              <motion.div
                key={property.id}
                whileTap={{ scale: 0.98 }}
                className="relative transition"
              >
                {/* Nút thêm so sánh phía trên bên phải */}
                <div className="absolute top-2 right-2 z-10">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => {
                      onSelectProperty(property);
                      onOpenChange(false);
                    }}
                  >
                    + Thêm so sánh
                  </Button>
                </div>
                <div
                  className="cursor-pointer"
                  onClick={() => {
                    onSelectProperty(property);
                    onOpenChange(false);
                  }}
                >
                  <PropertyCard property={property} size="md" />
                </div>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        <div className="flex justify-end">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Đóng
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
