import { useState, useCallback, useMemo, memo } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useCollections } from '@/hooks/useCollections';
import { Collection } from '@/lib/api/services/fetchCollection';
import { Folder, Plus, MoreHorizontal, Edit, Trash2 } from 'lucide-react';
import CreateCollectionModal from '@/components/collection/CreateCollectionModal';
import EditCollectionModal from '@/components/collection/EditCollectionModal';
import DeleteConfirmationDialog from '@/components/collection/DeleteConfirmationDialog';
import CollectionDetailView from './CollectionDetailView';
import Image from 'next/image';

// Utility function
const getTimeAgo = (updatedAt: string): string => {
  const diffInMinutes = Math.floor((Date.now() - new Date(updatedAt).getTime()) / 60000);
  if (diffInMinutes < 1) return 'vừa xong';
  if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} giờ trước`;
  return `${Math.floor(diffInMinutes / 1440)} ngày trước`;
};

// Collection thumbnail component
const CollectionThumbnail = memo(
  ({
    images,
    collectionName,
    className = '',
    priority = false,
  }: {
    images: string[];
    collectionName: string;
    className?: string;
    priority?: boolean;
  }) => {
    // Validate and filter images
    const validImages = images.filter(img => img && img.trim() !== '').slice(0, 4); // Allow up to 4 images

    return (
      <div className={`rounded-lg overflow-hidden bg-gray-100 ${className}`}>
        {validImages.length === 0 ? (
          <div className="w-full h-full flex items-center justify-center">
            <Folder className="w-8 h-8 text-gray-400" />
          </div>
        ) : (
          <div className="w-full h-full flex flex-col gap-0.5">
            <div className="flex-[2] relative bg-gray-200">
              <Image
                src={validImages[0]}
                alt={`Bộ sưu tập ${collectionName} - Hình ảnh chính`}
                fill
                className="object-cover"
                sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                priority={priority}
                loading={priority ? undefined : 'lazy'}
                onError={e => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            </div>
            <div className="flex-1 flex gap-0.5">
              {[1, 2, 3].map(index => (
                <div key={index} className="flex-1 relative bg-gray-200">
                  {validImages[index] ? (
                    <Image
                      src={validImages[index]}
                      alt={`Bộ sưu tập ${collectionName} - Hình ảnh ${index + 1}`}
                      fill
                      className="object-cover"
                      sizes="(max-width: 640px) 33vw, (max-width: 1024px) 16vw, 11vw"
                      loading="lazy"
                      onError={e => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-300" />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }
);
CollectionThumbnail.displayName = 'CollectionThumbnail';

// Collection card component
const CollectionCard = memo(
  ({
    collection,
    onViewCollection,
    onEditCollection,
    onDeleteCollection,
    isFirst = false,
  }: {
    collection: Collection;
    onViewCollection: (collection: Collection) => void;
    onEditCollection: (collection: Collection) => void;
    onDeleteCollection: (collection: Collection) => void;
    isFirst?: boolean;
  }) => {
    const timeAgo = useMemo(() => getTimeAgo(collection.updatedAt), [collection.updatedAt]);

    return (
      <Card
        className="cursor-pointer hover:shadow-lg transition-all duration-200 border-0 shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98] group"
        onClick={() => onViewCollection(collection)}
      >
        <div className="relative">
          <CollectionThumbnail
            images={collection.collectionImage || []}
            collectionName={collection.name}
            className="w-full h-48 sm:h-56 lg:h-64"
            priority={isFirst}
          />
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-3 right-3 h-8 w-8 bg-white/80 hover:bg-white/90 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                onClick={e => e.stopPropagation()}
                aria-label={`Tùy chọn cho bộ sưu tập ${collection.name}`}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" onCloseAutoFocus={e => e.preventDefault()}>
              <DropdownMenuItem
                onClick={e => {
                  e.stopPropagation();
                  onEditCollection(collection);
                }}
              >
                <Edit className="h-4 w-4 mr-2" />
                Chỉnh sửa
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={e => {
                  e.stopPropagation();
                  onDeleteCollection(collection);
                }}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Xóa
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <CardContent className="p-4 sm:p-5 space-y-3">
          <div className="flex items-start justify-between gap-3">
            <h3 className="text-base sm:text-lg font-semibold text-gray-900 leading-tight line-clamp-2 flex-1">
              {collection.name}
            </h3>
            <Badge
              variant="secondary"
              className="bg-blue-50 text-blue-700 text-xs px-2 py-1 flex-shrink-0"
            >
              Bộ sưu tập
            </Badge>
          </div>

          {collection.description && (
            <p className="text-sm text-gray-600 line-clamp-2 leading-relaxed">
              {collection.description}
            </p>
          )}

          <div className="flex items-center justify-between text-sm text-gray-500 pt-2 border-t border-gray-100">
            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-700">{collection.itemCount}</span>
              <span className="text-gray-400">bất động sản</span>
            </div>
            <span className="text-xs text-gray-400">{timeAgo}</span>
          </div>
        </CardContent>
      </Card>
    );
  }
);
CollectionCard.displayName = 'CollectionCard';

export default function SavedHomes() {
  const [selectedCollection, setSelectedCollection] = useState<Collection | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCollection, setEditingCollection] = useState<Collection | null>(null);
  const [deletingCollection, setDeletingCollection] = useState<Collection | null>(null);
  const { collections, isLoading, error, deleteCollection, isDeletingCollection } =
    useCollections();

  const handleViewCollection = useCallback((collection: Collection) => {
    setSelectedCollection(collection);
  }, []);

  const handleCreateCollection = useCallback(() => {
    setShowCreateModal(true);
  }, []);

  const handleEditCollection = useCallback((collection: Collection) => {
    setEditingCollection(collection);
  }, []);

  const handleDeleteCollection = useCallback((collection: Collection) => {
    setDeletingCollection(collection);
  }, []);

  const handleConfirmDelete = useCallback(() => {
    if (deletingCollection) {
      deleteCollection(deletingCollection.id);
      setDeletingCollection(null);
    }
  }, [deletingCollection, deleteCollection]);

  const handleCancelDelete = useCallback(() => {
    setDeletingCollection(null);
  }, []);

  const handleCloseEditModal = useCallback(() => {
    setEditingCollection(null);
  }, []);

  const handleCloseCreateModal = useCallback(() => {
    setShowCreateModal(false);
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-4 sm:space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="space-y-2">
            <Skeleton className="h-6 sm:h-7 w-48 sm:w-56" />
            <Skeleton className="h-4 w-64 sm:w-80" />
          </div>
          <Skeleton className="h-10 w-full sm:w-40" />
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="animate-pulse">
              <div className="aspect-[4/3] bg-gray-200 rounded-t-lg">
                <Skeleton className="h-full w-full rounded-t-lg" />
              </div>
              <CardContent className="p-4 space-y-3">
                <div className="flex items-start justify-between gap-3">
                  <Skeleton className="h-5 w-32 flex-1" />
                  <Skeleton className="h-6 w-16 rounded-full" />
                </div>
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <div className="flex items-center justify-between pt-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-3 w-16" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-16 sm:py-20">
        <div className="mx-auto max-w-md px-4">
          <div className="text-red-500 mb-4">
            <svg
              className="h-16 w-16 mx-auto"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833-.23 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Có lỗi xảy ra</h3>
          <p className="text-sm text-gray-500 mb-6">
            Không thể tải dữ liệu bộ sưu tập. Vui lòng thử lại sau.
          </p>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            className="w-full sm:w-auto"
          >
            Thử lại
          </Button>
        </div>
      </div>
    );
  }

  // Collection detail view
  if (selectedCollection) {
    return (
      <CollectionDetailView
        collection={selectedCollection}
        onBack={() => setSelectedCollection(null)}
      />
    );
  }

  // Main view
  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between sm:gap-6">
        <div className="space-y-2">
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold leading-tight">
            Bộ sưu tập của bạn
          </h1>
          <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
            Sắp xếp các bất động sản đã lưu thành bộ sưu tập
          </p>
        </div>
        <Button
          onClick={handleCreateCollection}
          className="w-full sm:w-auto sm:flex-shrink-0 bg-gray-900 hover:bg-gray-800 text-white font-medium px-4 sm:px-6 py-2 rounded-lg transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          <span className="hidden xs:inline">Tạo bộ sưu tập mới</span>
          <span className="xs:hidden">Tạo mới</span>
        </Button>
      </div>

      {/* Collections grid or empty state */}
      {collections.length === 0 ? (
        <div className="text-center py-16 sm:py-20">
          <div className="mx-auto max-w-md px-4">
            <div className="w-20 h-20 sm:w-24 sm:h-24 mx-auto mb-6 rounded-lg bg-gray-100 flex items-center justify-center">
              <Folder className="w-8 h-8 sm:w-10 sm:h-10 text-gray-400" />
            </div>
            <h3 className="text-lg sm:text-xl font-medium text-gray-900 mb-3">
              Chưa có bộ sưu tập nào
            </h3>
            <p className="text-sm sm:text-base text-gray-500 leading-relaxed mb-8">
              Bắt đầu bằng cách tạo bộ sưu tập đầu tiên để sắp xếp các bất động sản đã lưu
            </p>
            <Button
              onClick={handleCreateCollection}
              className="w-full sm:w-auto bg-gray-900 hover:bg-gray-800 text-white font-medium px-6 py-3 rounded-lg transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              Tạo bộ sưu tập đầu tiên
            </Button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-6">
          {collections.map((collection, index) => (
            <CollectionCard
              key={collection.id}
              collection={collection}
              onViewCollection={handleViewCollection}
              onEditCollection={handleEditCollection}
              onDeleteCollection={handleDeleteCollection}
              isFirst={index === 0}
            />
          ))}
        </div>
      )}

      <CreateCollectionModal isOpen={showCreateModal} onClose={handleCloseCreateModal} />
      <EditCollectionModal
        collection={editingCollection}
        isOpen={!!editingCollection}
        onClose={handleCloseEditModal}
      />
      <DeleteConfirmationDialog
        isOpen={!!deletingCollection}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Xóa bộ sưu tập"
        description={`Bạn có chắc chắn muốn xóa bộ sưu tập này không? Tất cả bất động sản trong bộ sưu tập sẽ bị xóa khỏi danh sách yêu thích của bạn.`}
        itemName={deletingCollection?.name}
        type="collection"
        isLoading={isDeletingCollection}
      />
    </div>
  );
}
