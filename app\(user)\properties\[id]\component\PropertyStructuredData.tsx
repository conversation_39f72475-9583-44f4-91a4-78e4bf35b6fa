import { Property } from '@/lib/api/services/fetchProperty';

interface PropertyStructuredDataProps {
  property: Property;
}

export default function PropertyStructuredData({ property }: PropertyStructuredDataProps) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'RealEstateListing',
    name: property.title,
    description: property.description,
    url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://revoland.com'}/properties/${property.id}`,
    image: property.imageUrls?.[0] || '',
    datePosted: property.createdAt,
    dateModified: property.updatedAt,
    provider: {
      '@type': 'Organization',
      name: 'RevoLand',
      url: process.env.NEXT_PUBLIC_BASE_URL || 'https://revoland.com',
    },
    location: {
      '@type': 'Place',
      address: {
        '@type': 'PostalAddress',
        streetAddress: property.location.address,
        addressLocality: property.location.ward,
        addressRegion: property.location.district,
        addressCountry: 'VN',
      },
      geo: {
        '@type': 'GeoCoordinates',
        latitude: property.location.latitude,
        longitude: property.location.longitude,
      },
    },
    offers: {
      '@type': 'Offer',
      price:
        property.transactionType === 'ForSale'
          ? property.priceDetails.salePrice
          : property.priceDetails.rentalPrice,
      priceCurrency: property.priceDetails.currency,
      availability:
        property.status === 'Available'
          ? 'https://schema.org/InStock'
          : 'https://schema.org/OutOfStock',
      validFrom: property.createdAt,
      seller: {
        '@type': 'Person',
        name: property.saler.fullName,
        email: property.saler.email,
        telephone: property.saler.phoneNumber,
      },
    },
    floorSize: property.propertyDetails.landArea
      ? {
          '@type': 'QuantitativeValue',
          value: property.propertyDetails.landArea,
          unitCode: 'MTK',
        }
      : undefined,
    numberOfBedrooms: property.propertyDetails.bedrooms,
    numberOfBathroomsTotal: property.propertyDetails.bathrooms,
    yearBuilt: property.yearBuilt,
    additionalProperty: [
      {
        '@type': 'PropertyValue',
        name: 'Property Type',
        value: property.type,
      },
      {
        '@type': 'PropertyValue',
        name: 'Transaction Type',
        value: property.transactionType,
      },
      {
        '@type': 'PropertyValue',
        name: 'Status',
        value: property.status,
      },
      ...(property.propertyDetails.bedrooms
        ? [
            {
              '@type': 'PropertyValue',
              name: 'Bedrooms',
              value: property.propertyDetails.bedrooms,
            },
          ]
        : []),
      ...(property.propertyDetails.bathrooms
        ? [
            {
              '@type': 'PropertyValue',
              name: 'Bathrooms',
              value: property.propertyDetails.bathrooms,
            },
          ]
        : []),
      ...(property.propertyDetails.landArea
        ? [
            {
              '@type': 'PropertyValue',
              name: 'Land Area',
              value: `${property.propertyDetails.landArea} m²`,
            },
          ]
        : []),
      ...(property.propertyDetails.buildingArea
        ? [
            {
              '@type': 'PropertyValue',
              name: 'Building Area',
              value: `${property.propertyDetails.buildingArea} m²`,
            },
          ]
        : []),
      ...(property.propertyDetails.numberOfFloors
        ? [
            {
              '@type': 'PropertyValue',
              name: 'Number of Floors',
              value: property.propertyDetails.numberOfFloors,
            },
          ]
        : []),
    ],
    amenityFeature: [
      ...(property.amenities.parking
        ? [
            {
              '@type': 'LocationFeatureSpecification',
              name: 'Parking',
              value: true,
            },
          ]
        : []),
      ...(property.amenities.elevator
        ? [
            {
              '@type': 'LocationFeatureSpecification',
              name: 'Elevator',
              value: true,
            },
          ]
        : []),
      ...(property.amenities.swimmingPool
        ? [
            {
              '@type': 'LocationFeatureSpecification',
              name: 'Swimming Pool',
              value: true,
            },
          ]
        : []),
      ...(property.amenities.gym
        ? [
            {
              '@type': 'LocationFeatureSpecification',
              name: 'Gym',
              value: true,
            },
          ]
        : []),
      ...(property.amenities.securitySystem
        ? [
            {
              '@type': 'LocationFeatureSpecification',
              name: 'Security System',
              value: true,
            },
          ]
        : []),
      ...(property.amenities.airConditioning
        ? [
            {
              '@type': 'LocationFeatureSpecification',
              name: 'Air Conditioning',
              value: true,
            },
          ]
        : []),
      ...(property.amenities.balcony
        ? [
            {
              '@type': 'LocationFeatureSpecification',
              name: 'Balcony',
              value: true,
            },
          ]
        : []),
      ...(property.amenities.garden
        ? [
            {
              '@type': 'LocationFeatureSpecification',
              name: 'Garden',
              value: true,
            },
          ]
        : []),
      ...(property.amenities.playground
        ? [
            {
              '@type': 'LocationFeatureSpecification',
              name: 'Playground',
              value: true,
            },
          ]
        : []),
      ...(property.amenities.backupGenerator
        ? [
            {
              '@type': 'LocationFeatureSpecification',
              name: 'Backup Generator',
              value: true,
            },
          ]
        : []),
    ].filter(Boolean),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}
