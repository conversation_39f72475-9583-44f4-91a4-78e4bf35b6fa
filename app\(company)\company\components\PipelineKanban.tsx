import React, { useState } from 'react';
import {
  ArrowLeft,
  Plus,
  Search,
  MoreVertical,
  Phone,
  Mail,
  Calendar,
  User,
  MapPin,
  Building2,
  Clock,
  Edit,
  DollarSign,
  Target,
  TrendingUp,
  Users,
  RefreshCw,
  Download,
  Globe,
  Facebook,
  MessageCircle,
  Activity,
  CheckCircle,
  XCircle,
  GripVertical,
  FileText,
} from 'lucide-react';

interface PipelineKanbanProps {
  onBack: () => void;
}

interface Deal {
  id: string;
  title: string;
  client: {
    name: string;
    email: string;
    phone: string;
    avatar: string;
  };
  property: {
    name: string;
    type: string;
    location: string;
    price: number;
  };
  value: number;
  probability: number;
  stage: string;
  priority: 'high' | 'medium' | 'low';
  source: 'website' | 'facebook' | 'zalo' | 'referral' | 'phone' | 'email' | 'walk-in';
  assignedAgent: string;
  createdAt: string;
  updatedAt: string;
  expectedCloseDate: string;
  lastActivity: string;
  tags: string[];
  notes: string;
  activities: number;
}

interface Stage {
  id: string;
  name: string;
  color: string;
  deals: Deal[];
  probability: number;
  description: string;
}

const PipelineKanban: React.FC<PipelineKanbanProps> = ({ onBack }) => {
  const [stages, setStages] = useState<Stage[]>([
    {
      id: 'new',
      name: 'Mới',
      color: 'from-blue-500 to-cyan-500',
      probability: 10,
      description: 'Leads mới chưa được liên hệ',
      deals: [
        {
          id: '1',
          title: 'Căn hộ Vinhomes Central Park',
          client: {
            name: 'Nguyễn Minh Hoàng',
            email: '<EMAIL>',
            phone: '0123456789',
            avatar: 'NMH',
          },
          property: {
            name: 'Căn hộ Vinhomes Central Park',
            type: 'Căn hộ',
            location: 'Quận 1, TP.HCM',
            price: 5200000000,
          },
          value: 5200000000,
          probability: 10,
          stage: 'new',
          priority: 'high',
          source: 'website',
          assignedAgent: 'Nguyễn Văn A',
          createdAt: '2024-01-20',
          updatedAt: '2024-01-20',
          expectedCloseDate: '2024-02-15',
          lastActivity: '2 giờ trước',
          tags: ['VIP', 'Hot Lead'],
          notes: 'Khách hàng quan tâm căn 2PN, tầng cao, view sông',
          activities: 3,
        },
        {
          id: '2',
          title: 'Shophouse Saigon South',
          client: {
            name: 'Vũ Thị Hương',
            email: '<EMAIL>',
            phone: '0321654987',
            avatar: 'VTH',
          },
          property: {
            name: 'Shophouse Saigon South',
            type: 'Shophouse',
            location: 'Quận 7, TP.HCM',
            price: 18000000000,
          },
          value: 18000000000,
          probability: 15,
          stage: 'new',
          priority: 'medium',
          source: 'facebook',
          assignedAgent: 'Trần Thị B',
          createdAt: '2024-01-19',
          updatedAt: '2024-01-20',
          expectedCloseDate: '2024-03-01',
          lastActivity: '1 ngày trước',
          tags: ['Investment', 'Commercial'],
          notes: 'Khách đầu tư, quan tâm vị trí mặt tiền',
          activities: 2,
        },
      ],
    },
    {
      id: 'contacted',
      name: 'Đã liên hệ',
      color: 'from-yellow-500 to-orange-500',
      probability: 25,
      description: 'Đã liên hệ và xác nhận nhu cầu',
      deals: [
        {
          id: '3',
          title: 'Biệt thự Phú Mỹ Hưng',
          client: {
            name: 'Lê Thị Mai',
            email: '<EMAIL>',
            phone: '0987654321',
            avatar: 'LTM',
          },
          property: {
            name: 'Biệt thự Phú Mỹ Hưng',
            type: 'Biệt thự',
            location: 'Quận 7, TP.HCM',
            price: 12000000000,
          },
          value: 12000000000,
          probability: 30,
          stage: 'contacted',
          priority: 'high',
          source: 'referral',
          assignedAgent: 'Lê Văn C',
          createdAt: '2024-01-18',
          updatedAt: '2024-01-20',
          expectedCloseDate: '2024-02-20',
          lastActivity: '3 giờ trước',
          tags: ['Luxury', 'Family'],
          notes: 'Đã gọi điện, khách quan tâm xem nhà cuối tuần',
          activities: 5,
        },
      ],
    },
    {
      id: 'appointment',
      name: 'Hẹn xem nhà',
      color: 'from-green-500 to-emerald-500',
      probability: 40,
      description: 'Đã đặt lịch xem nhà',
      deals: [
        {
          id: '4',
          title: 'Chung cư The Manor',
          client: {
            name: 'Trần Văn Nam',
            email: '<EMAIL>',
            phone: '0369852147',
            avatar: 'TVN',
          },
          property: {
            name: 'Chung cư The Manor',
            type: 'Chung cư',
            location: 'Quận 5, TP.HCM',
            price: 3800000000,
          },
          value: 3800000000,
          probability: 45,
          stage: 'appointment',
          priority: 'medium',
          source: 'zalo',
          assignedAgent: 'Phạm Thị D',
          createdAt: '2024-01-17',
          updatedAt: '2024-01-19',
          expectedCloseDate: '2024-02-10',
          lastActivity: '1 ngày trước',
          tags: ['First Time Buyer'],
          notes: 'Lịch xem nhà: Thứ 7, 10:00 AM',
          activities: 8,
        },
      ],
    },
    {
      id: 'proposal',
      name: 'Đề xuất',
      color: 'from-purple-500 to-pink-500',
      probability: 60,
      description: 'Đã gửi đề xuất và báo giá',
      deals: [
        {
          id: '5',
          title: 'Nhà phố Thảo Điền',
          client: {
            name: 'Phạm Thị Lan',
            email: '<EMAIL>',
            phone: '0258147963',
            avatar: 'PTL',
          },
          property: {
            name: 'Nhà phố Thảo Điền',
            type: 'Nhà phố',
            location: 'Quận 2, TP.HCM',
            price: 8500000000,
          },
          value: 8500000000,
          probability: 65,
          stage: 'proposal',
          priority: 'high',
          source: 'email',
          assignedAgent: 'Nguyễn Văn A',
          createdAt: '2024-01-15',
          updatedAt: '2024-01-20',
          expectedCloseDate: '2024-02-05',
          lastActivity: '30 phút trước',
          tags: ['Hot Lead', 'Investment'],
          notes: 'Đã gửi proposal, chờ phản hồi từ khách',
          activities: 12,
        },
      ],
    },
    {
      id: 'negotiation',
      name: 'Thương lượng',
      color: 'from-orange-500 to-red-500',
      probability: 80,
      description: 'Đang thương lượng giá và điều khoản',
      deals: [
        {
          id: '6',
          title: 'Căn hộ Landmark 81',
          client: {
            name: 'Hoàng Văn Đức',
            email: '<EMAIL>',
            phone: '0147258369',
            avatar: 'HVD',
          },
          property: {
            name: 'Căn hộ Landmark 81',
            type: 'Căn hộ',
            location: 'Quận 1, TP.HCM',
            price: 15000000000,
          },
          value: 15000000000,
          probability: 85,
          stage: 'negotiation',
          priority: 'high',
          source: 'phone',
          assignedAgent: 'Trần Thị B',
          createdAt: '2024-01-10',
          updatedAt: '2024-01-20',
          expectedCloseDate: '2024-01-25',
          lastActivity: '1 giờ trước',
          tags: ['VIP', 'Luxury', 'Negotiating'],
          notes: 'Đang thương lượng giá, khách muốn giảm 5%',
          activities: 25,
        },
      ],
    },
    {
      id: 'closed',
      name: 'Thành công',
      color: 'from-emerald-500 to-green-500',
      probability: 100,
      description: 'Giao dịch hoàn thành',
      deals: [
        {
          id: '7',
          title: 'Penthouse Sky Villa',
          client: {
            name: 'Đỗ Minh Quân',
            email: '<EMAIL>',
            phone: '0456789123',
            avatar: 'DMQ',
          },
          property: {
            name: 'Penthouse Sky Villa',
            type: 'Penthouse',
            location: 'Quận 1, TP.HCM',
            price: 25000000000,
          },
          value: 25000000000,
          probability: 100,
          stage: 'closed',
          priority: 'high',
          source: 'referral',
          assignedAgent: 'Nguyễn Văn A',
          createdAt: '2024-01-05',
          updatedAt: '2024-01-18',
          expectedCloseDate: '2024-01-18',
          lastActivity: '2 ngày trước',
          tags: ['VIP', 'Luxury', 'Closed'],
          notes: 'Giao dịch hoàn thành, ký hợp đồng thành công',
          activities: 35,
        },
      ],
    },
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [filterAgent, setFilterAgent] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  const [draggedDeal, setDraggedDeal] = useState<Deal | null>(null);
  const [dragOverStage, setDragOverStage] = useState<string | null>(null);
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);

  const priorityConfig = {
    high: { label: 'Cao', color: 'bg-red-100 text-red-800', icon: '🔥' },
    medium: { label: 'Trung bình', color: 'bg-yellow-100 text-yellow-800', icon: '⚡' },
    low: { label: 'Thấp', color: 'bg-gray-100 text-gray-800', icon: '📋' },
  };

  const sourceConfig = {
    website: { label: 'Website', icon: Globe, color: 'text-blue-600 bg-blue-100' },
    facebook: { label: 'Facebook', icon: Facebook, color: 'text-blue-600 bg-blue-100' },
    zalo: { label: 'Zalo', icon: MessageCircle, color: 'text-blue-600 bg-blue-100' },
    referral: { label: 'Giới thiệu', icon: Users, color: 'text-green-600 bg-green-100' },
    phone: { label: 'Điện thoại', icon: Phone, color: 'text-purple-600 bg-purple-100' },
    email: { label: 'Email', icon: Mail, color: 'text-orange-600 bg-orange-100' },
    'walk-in': { label: 'Khách vãng lai', icon: User, color: 'text-gray-600 bg-gray-100' },
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      notation: 'compact',
      maximumFractionDigits: 1,
    }).format(amount);
  };

  const handleDragStart = (deal: Deal) => {
    setDraggedDeal(deal);
  };

  const handleDragOver = (e: React.DragEvent, stageId: string) => {
    e.preventDefault();
    setDragOverStage(stageId);
  };

  const handleDragLeave = () => {
    setDragOverStage(null);
  };

  const handleDrop = (e: React.DragEvent, targetStageId: string) => {
    e.preventDefault();
    setDragOverStage(null);

    if (!draggedDeal || draggedDeal.stage === targetStageId) return;

    // Update stages
    setStages(prev =>
      prev.map(stage => {
        if (stage.id === draggedDeal.stage) {
          // Remove deal from source stage
          return {
            ...stage,
            deals: stage.deals.filter(deal => deal.id !== draggedDeal.id),
          };
        }
        if (stage.id === targetStageId) {
          // Add deal to target stage
          const updatedDeal = {
            ...draggedDeal,
            stage: targetStageId,
            probability: stage.probability,
            updatedAt: new Date().toISOString().split('T')[0],
          };
          return {
            ...stage,
            deals: [...stage.deals, updatedDeal],
          };
        }
        return stage;
      })
    );

    setDraggedDeal(null);
  };

  const getTotalValue = () => {
    return stages.reduce(
      (total, stage) =>
        total + stage.deals.reduce((stageTotal, deal) => stageTotal + deal.value, 0),
      0
    );
  };

  const getWeightedValue = () => {
    return stages.reduce(
      (total, stage) =>
        total +
        stage.deals.reduce(
          (stageTotal, deal) => stageTotal + (deal.value * deal.probability) / 100,
          0
        ),
      0
    );
  };

  const getTotalDeals = () => {
    return stages.reduce((total, stage) => total + stage.deals.length, 0);
  };

  return (
    <div className="flex-1 bg-gradient-to-br from-gray-50 to-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-100 px-8 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <button
              onClick={onBack}
              className="flex items-center gap-3 text-gray-600 hover:text-gray-900 transition-colors font-medium"
            >
              <ArrowLeft size={20} />
              <span>Quay lại</span>
            </button>
            <div className="h-6 w-px bg-gray-300"></div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Pipeline Kanban</h1>
              <p className="text-gray-600">Quản lý quy trình bán hàng</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <button className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white rounded-xl font-medium shadow-lg shadow-red-500/25 transition-all">
              <Plus size={16} />
              <span>Thêm Deal</span>
            </button>
            <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl border border-gray-200 transition-all">
              <Download size={16} />
              <span className="text-sm font-medium">Xuất báo cáo</span>
            </button>
            <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl border border-gray-200 transition-all">
              <RefreshCw size={16} />
              <span className="text-sm font-medium">Làm mới</span>
            </button>
          </div>
        </div>
      </div>

      <div className="p-8">
        <div className="max-w-full mx-auto space-y-8">
          {/* Pipeline Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-2xl border border-gray-200 p-6 shadow-sm">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <Target size={24} className="text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{getTotalDeals()}</p>
                  <p className="text-gray-600 text-sm">Tổng Deals</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl border border-gray-200 p-6 shadow-sm">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <DollarSign size={24} className="text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(getTotalValue())}
                  </p>
                  <p className="text-gray-600 text-sm">Tổng Giá trị</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl border border-gray-200 p-6 shadow-sm">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                  <TrendingUp size={24} className="text-purple-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(getWeightedValue())}
                  </p>
                  <p className="text-gray-600 text-sm">Giá trị Dự kiến</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl border border-gray-200 p-6 shadow-sm">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                  <CheckCircle size={24} className="text-orange-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {stages.find(s => s.id === 'closed')?.deals.length || 0}
                  </p>
                  <p className="text-gray-600 text-sm">Thành công</p>
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-2xl border border-gray-200 shadow-sm p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                    size={18}
                  />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    className="w-full pl-12 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                    placeholder="Tìm kiếm deals..."
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <select
                  value={filterAgent}
                  onChange={e => setFilterAgent(e.target.value)}
                  className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                >
                  <option value="all">Tất cả Agent</option>
                  <option value="Nguyễn Văn A">Nguyễn Văn A</option>
                  <option value="Trần Thị B">Trần Thị B</option>
                  <option value="Lê Văn C">Lê Văn C</option>
                  <option value="Phạm Thị D">Phạm Thị D</option>
                </select>

                <select
                  value={filterPriority}
                  onChange={e => setFilterPriority(e.target.value)}
                  className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                >
                  <option value="all">Tất cả độ ưu tiên</option>
                  <option value="high">Cao</option>
                  <option value="medium">Trung bình</option>
                  <option value="low">Thấp</option>
                </select>
              </div>
            </div>
          </div>

          {/* Kanban Board */}
          <div className="bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden">
            <div className="p-6 border-b border-gray-100">
              <h3 className="text-lg font-bold text-gray-900">Pipeline Stages</h3>
              <p className="text-gray-600 text-sm">Kéo thả để di chuyển deals giữa các giai đoạn</p>
            </div>

            <div className="p-6">
              <div className="flex gap-6 overflow-x-auto pb-4">
                {stages.map(stage => {
                  const filteredDeals = stage.deals.filter(deal => {
                    const matchesSearch =
                      deal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                      deal.client.name.toLowerCase().includes(searchTerm.toLowerCase());
                    const matchesAgent =
                      filterAgent === 'all' || deal.assignedAgent === filterAgent;
                    const matchesPriority =
                      filterPriority === 'all' || deal.priority === filterPriority;
                    return matchesSearch && matchesAgent && matchesPriority;
                  });

                  const stageValue = filteredDeals.reduce((sum, deal) => sum + deal.value, 0);

                  return (
                    <div
                      key={stage.id}
                      onDragOver={e => handleDragOver(e, stage.id)}
                      onDragLeave={handleDragLeave}
                      onDrop={e => handleDrop(e, stage.id)}
                      className={`flex-shrink-0 w-80 bg-gray-50 rounded-xl transition-all duration-300 ${
                        dragOverStage === stage.id
                          ? 'bg-red-50 border-2 border-red-300 scale-105 shadow-lg'
                          : 'border border-gray-200'
                      }`}
                    >
                      {/* Stage Header */}
                      <div
                        className={`p-4 bg-gradient-to-r ${stage.color} rounded-t-xl text-white`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-bold text-lg">{stage.name}</h4>
                          <span className="bg-white/20 px-2 py-1 rounded-full text-sm font-medium">
                            {filteredDeals.length}
                          </span>
                        </div>
                        <p className="text-white/90 text-sm mb-3">{stage.description}</p>
                        <div className="flex items-center justify-between text-sm">
                          <span className="bg-white/20 px-2 py-1 rounded-lg">
                            {stage.probability}% xác suất
                          </span>
                          <span className="font-semibold">{formatCurrency(stageValue)}</span>
                        </div>
                      </div>

                      {/* Deals List */}
                      <div className="p-4 space-y-4 min-h-[400px] max-h-[600px] overflow-y-auto">
                        {filteredDeals.length === 0 ? (
                          <div className="flex items-center justify-center h-32 border-2 border-dashed border-gray-200 rounded-xl">
                            <p className="text-gray-400 text-sm">Kéo thả deals vào đây</p>
                          </div>
                        ) : (
                          filteredDeals.map(deal => {
                            const priorityInfo = priorityConfig[deal.priority];
                            const sourceInfo = sourceConfig[deal.source];
                            const SourceIcon = sourceInfo.icon;

                            return (
                              <div
                                key={deal.id}
                                draggable
                                onDragStart={() => handleDragStart(deal)}
                                onClick={() => setSelectedDeal(deal)}
                                className="bg-white rounded-xl border border-gray-200 p-4 shadow-sm hover:shadow-md transition-all cursor-move group hover:scale-[1.02]"
                              >
                                {/* Deal Header */}
                                <div className="flex items-start justify-between mb-3">
                                  <div className="flex-1">
                                    <h5 className="font-semibold text-gray-900 mb-1 line-clamp-2">
                                      {deal.title}
                                    </h5>
                                    <div className="flex items-center gap-2">
                                      <span
                                        className={`px-2 py-0.5 rounded-full text-xs font-medium ${priorityInfo.color}`}
                                      >
                                        {priorityInfo.icon}
                                      </span>
                                      <div
                                        className={`flex items-center gap-1 px-2 py-0.5 rounded-lg ${sourceInfo.color}`}
                                      >
                                        <SourceIcon size={10} />
                                        <span className="text-xs font-medium">
                                          {sourceInfo.label}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                                    <GripVertical size={16} className="text-gray-400" />
                                  </div>
                                </div>

                                {/* Client Info */}
                                <div className="flex items-center gap-3 mb-3">
                                  <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white font-semibold text-xs">
                                    {deal.client.avatar}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <p className="font-medium text-gray-900 text-sm truncate">
                                      {deal.client.name}
                                    </p>
                                    <p className="text-gray-500 text-xs truncate">
                                      {deal.client.email}
                                    </p>
                                  </div>
                                </div>

                                {/* Property Info */}
                                <div className="mb-3">
                                  <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                                    <Building2 size={12} />
                                    <span className="truncate">{deal.property.type}</span>
                                  </div>
                                  <div className="flex items-center gap-2 text-sm text-gray-600">
                                    <MapPin size={12} />
                                    <span className="truncate">{deal.property.location}</span>
                                  </div>
                                </div>

                                {/* Deal Value */}
                                <div className="mb-3">
                                  <p className="text-lg font-bold text-gray-900">
                                    {formatCurrency(deal.value)}
                                  </p>
                                  <div className="flex items-center gap-2 text-sm text-gray-600">
                                    <Target size={12} />
                                    <span>{deal.probability}% xác suất</span>
                                  </div>
                                </div>

                                {/* Tags */}
                                {deal.tags.length > 0 && (
                                  <div className="mb-3">
                                    <div className="flex flex-wrap gap-1">
                                      {deal.tags.slice(0, 2).map((tag, index) => (
                                        <span
                                          key={index}
                                          className="px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs font-medium"
                                        >
                                          {tag}
                                        </span>
                                      ))}
                                      {deal.tags.length > 2 && (
                                        <span className="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">
                                          +{deal.tags.length - 2}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                )}

                                {/* Footer */}
                                <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                                  <div className="flex items-center gap-2 text-xs text-gray-500">
                                    <Clock size={10} />
                                    <span>{deal.lastActivity}</span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <button
                                      onClick={e => {
                                        e.stopPropagation();
                                        // Handle phone call
                                      }}
                                      className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all"
                                    >
                                      <Phone size={12} />
                                    </button>
                                    <button
                                      onClick={e => {
                                        e.stopPropagation();
                                        // Handle email
                                      }}
                                      className="p-1.5 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all"
                                    >
                                      <Mail size={12} />
                                    </button>
                                    <button
                                      onClick={e => {
                                        e.stopPropagation();
                                        // Handle more options
                                      }}
                                      className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-all"
                                    >
                                      <MoreVertical size={12} />
                                    </button>
                                  </div>
                                </div>
                              </div>
                            );
                          })
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Deal Detail Modal */}
          {selectedDeal && (
            <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-2xl border border-gray-200 shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <div className="p-6 border-b border-gray-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white font-semibold">
                        {selectedDeal.client.avatar}
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{selectedDeal.title}</h3>
                        <p className="text-gray-600">{selectedDeal.client.name}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => setSelectedDeal(null)}
                      className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-all"
                    >
                      <XCircle size={24} />
                    </button>
                  </div>
                </div>

                <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Left Column */}
                    <div className="space-y-6">
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">
                          Thông tin khách hàng
                        </h4>
                        <div className="space-y-3">
                          <div className="flex items-center gap-3">
                            <Mail size={16} className="text-gray-400" />
                            <span className="text-gray-900">{selectedDeal.client.email}</span>
                          </div>
                          <div className="flex items-center gap-3">
                            <Phone size={16} className="text-gray-400" />
                            <span className="text-gray-900">{selectedDeal.client.phone}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">Thông tin BDS</h4>
                        <div className="bg-gray-50 p-4 rounded-xl space-y-3">
                          <h5 className="font-semibold text-gray-900">
                            {selectedDeal.property.name}
                          </h5>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Building2 size={14} />
                            <span>{selectedDeal.property.type}</span>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <MapPin size={14} />
                            <span>{selectedDeal.property.location}</span>
                          </div>
                          <p className="text-lg font-bold text-gray-900">
                            {formatCurrency(selectedDeal.property.price)}
                          </p>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">Ghi chú</h4>
                        <p className="text-gray-700 bg-gray-50 p-4 rounded-xl">
                          {selectedDeal.notes}
                        </p>
                      </div>
                    </div>

                    {/* Right Column */}
                    <div className="space-y-6">
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">Chi tiết Deal</h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">Giá trị:</span>
                            <span className="font-semibold text-gray-900">
                              {formatCurrency(selectedDeal.value)}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">Xác suất:</span>
                            <span className="font-semibold text-gray-900">
                              {selectedDeal.probability}%
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">Agent:</span>
                            <span className="font-semibold text-gray-900">
                              {selectedDeal.assignedAgent}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">Ngày tạo:</span>
                            <span className="font-semibold text-gray-900">
                              {new Date(selectedDeal.createdAt).toLocaleDateString('vi-VN')}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">Dự kiến đóng:</span>
                            <span className="font-semibold text-gray-900">
                              {new Date(selectedDeal.expectedCloseDate).toLocaleDateString('vi-VN')}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">Tags</h4>
                        <div className="flex flex-wrap gap-2">
                          {selectedDeal.tags.map((tag, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">Hoạt động</h4>
                        <div className="bg-gray-50 p-4 rounded-xl">
                          <div className="flex items-center gap-3">
                            <Activity size={16} className="text-gray-400" />
                            <span className="text-gray-900">
                              {selectedDeal.activities} tương tác
                            </span>
                          </div>
                          <div className="flex items-center gap-3 mt-2">
                            <Clock size={16} className="text-gray-400" />
                            <span className="text-gray-600">
                              Lần cuối: {selectedDeal.lastActivity}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <div className="flex gap-4">
                      <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all">
                        <Phone size={16} />
                        Gọi điện
                      </button>
                      <button className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-xl transition-all">
                        <Mail size={16} />
                        Gửi email
                      </button>
                      <button className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-xl transition-all">
                        <Calendar size={16} />
                        Đặt lịch hẹn
                      </button>
                      <button className="flex items-center gap-2 px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-xl transition-all">
                        <FileText size={16} />
                        Tạo proposal
                      </button>
                      <button className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-xl transition-all">
                        <Edit size={16} />
                        Chỉnh sửa
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PipelineKanban;
