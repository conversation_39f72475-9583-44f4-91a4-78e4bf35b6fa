'use client';

import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  MessageCircle,
  Clock,
  Phone,
  Calculator,
  HandCoins,
  ChevronUp,
  ChevronDown,
  User,
} from 'lucide-react';
import { type Property, TransactionType } from '@/lib/api/services/fetchProperty';
import { useIsMobile } from '@/hooks/useMobile';
import { AnimatePresence, motion } from 'framer-motion';
import { useCallback, useState } from 'react';
import { useEffect } from 'react';

export default function ContactSideBar({
  property,
  onShowAppointment,
  onShowCalculationTool,
  onShowLoan,
  topPosition = 'top-4',
}: {
  property: Property;
  onShowAppointment: () => void;
  onShowCalculationTool: () => void;
  onShowLoan: () => void;
  topPosition?: string;
}) {
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const isMobile = useIsMobile();

  function formatPriceShort(value: number): string {
    if (value >= 1_000_000_000) {
      return (value / 1_000_000_000).toFixed(1).replace(/\.0$/, '') + ' tỷ';
    }
    if (value >= 1_000_000) {
      return (value / 1_000_000).toFixed(1).replace(/\.0$/, '') + ' triệu';
    }
    if (value >= 1_000) {
      return (value / 1_000).toFixed(1).replace(/\.0$/, '') + ' nghìn';
    }
    return value.toString();
  }

  // Scroll handler for mobile floating bar visibility
  const handleScroll = useCallback(() => {
    const showcaseElement = document.querySelector('#property-showcase');
    if (showcaseElement) {
      const showcaseRect = showcaseElement.getBoundingClientRect();
      const shouldShow = showcaseRect.bottom < 100; // Show when showcase is mostly out of view
      setIsVisible(shouldShow);
    }
  }, []);

  // Scroll event listener
  useEffect(() => {
    if (!isMobile) return;

    let timeoutId: NodeJS.Timeout;
    const throttledScrollHandler = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(handleScroll, 10);
    };

    window.addEventListener('scroll', throttledScrollHandler);
    return () => {
      window.removeEventListener('scroll', throttledScrollHandler);
      clearTimeout(timeoutId);
    };
  }, [handleScroll, isMobile]);
  // Mobile Floating Bar
  if (isMobile) {
    return (
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            transition={{
              type: 'spring',
              stiffness: 300,
              damping: 30,
            }}
            className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-2xl"
          >
            {/* Collapsed State - Always Visible */}
            <div className="px-4 py-3">
              <div className="flex items-center justify-between">
                {/* Price Info */}
                <div className="flex-1">
                  {property.transactionType === TransactionType.FOR_SALE ? (
                    <div>
                      <div className="text-lg font-semibold">
                        {formatPriceShort(property.priceDetails.salePrice || 0)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {formatPriceShort(property.priceDetails.pricePerSquareMeter || 0)}/m²
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="text-lg font-semibold">
                        {formatPriceShort(property.priceDetails.rentalPrice || 0)}
                        <span className="text-sm text-muted-foreground">/tháng</span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="flex items-center gap-1"
                  >
                    <span className="text-xs">{isExpanded ? 'Thu gọn' : 'Thêm'}</span>
                    {isExpanded ? (
                      <ChevronDown className="h-3 w-3" />
                    ) : (
                      <ChevronUp className="h-3 w-3" />
                    )}
                  </Button>
                  <Button className="bg-red-600 text-white hover:bg-red-700" size="sm">
                    <MessageCircle className="h-4 w-4 mr-1" />
                    <span className="text-sm">Liên hệ</span>
                  </Button>
                </div>
              </div>
            </div>

            {/* Expanded State */}
            <AnimatePresence>
              {isExpanded && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden border-t border-gray-100"
                >
                  <div className="px-4 py-4 space-y-4">
                    {/* Action Buttons */}
                    <div className="grid grid-cols-2 gap-3">
                      <div className="relative">
                        <Button className="w-full bg-red-600 text-white hover:bg-red-700" size="sm">
                          <MessageCircle className="mr-2 h-4 w-4" />
                          <span className="text-xs">Liên hệ môi giới</span>
                        </Button>
                        <Badge className="absolute -top-1 -right-1 text-[8px] px-1 border-transparent bg-gradient-to-r from-indigo-500 to-pink-500 text-white">
                          Soon
                        </Badge>
                      </div>

                      <Button
                        className="w-full"
                        size="sm"
                        variant="outline"
                        onClick={onShowAppointment}
                      >
                        <Clock className="mr-2 h-4 w-4" />
                        <span className="text-xs">Đặt lịch hẹn</span>
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 gap-3">
                      <div className="relative">
                        <Button
                          className="w-full"
                          size="sm"
                          variant="outline"
                          onClick={onShowCalculationTool}
                        >
                          <Calculator className="mr-2 h-4 w-4" />
                          <span className="text-xs">Tính toán khoản vay</span>
                        </Button>
                        <Badge className="absolute -top-1 -right-1 text-[8px] px-1 border-transparent bg-gradient-to-r from-indigo-500 to-pink-500 text-white">
                          Soon
                        </Badge>
                      </div>
                    </div>

                    <Separator />

                    {/* Agent Info */}
                    <div className="space-y-3">
                      <h4 className="font-medium text-sm">Liên hệ {property.saler.fullName}</h4>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
                          <User className="h-5 w-5 text-red-600" />
                        </div>
                        <div className="flex-1">
                          <p className="font-medium text-sm">{property.saler.fullName}</p>
                          <p className="text-xs text-muted-foreground">Liên hệ môi giới</p>
                        </div>
                        <Button variant="outline" size="sm" className="text-xs">
                          <Phone className="mr-1 h-3 w-3" />
                          Gọi
                        </Button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  // Desktop Sidebar
  return (
    <div className="lg:w-1/3">
      <Card className={`sticky ${topPosition}`}>
        <CardHeader>
          <CardTitle className="text-sm text-muted-foreground font-normal">
            {property.transactionType === TransactionType.FOR_SALE ? 'Giá bán' : 'Giá thuê'}
            {/* Price Display */}
            <div>
              {property.transactionType === TransactionType.FOR_SALE ? (
                <>
                  <div className="text-2xl md:text-3xl font-medium mb-2 text-foreground">
                    {formatPriceShort(property.priceDetails.salePrice || 0)}
                  </div>
                  <div className="text-lg text-foreground">
                    {formatPriceShort(property.priceDetails.pricePerSquareMeter || 0)}{' '}
                    <span className="text-sm text-muted-foreground">/m²</span>
                  </div>
                </>
              ) : (
                <>
                  <div className="text-2xl md:text-3xl font-medium mb-2 text-foreground">
                    {formatPriceShort(property.priceDetails.rentalPrice || 0)}{' '}
                    <span className="text-sm text-muted-foreground">/tháng</span>
                  </div>
                </>
              )}
            </div>
          </CardTitle>
          {/* <CardDescription>
            {property.transactionType === TransactionType.FOR_SALE 
              ? `Giá bán: ${formatPriceShort(property.priceDetails.salePrice || 0)}`
              : `Giá thuê: ${formatPriceShort(property.priceDetails.rentalPrice || 0)}/tháng`
            }
          </CardDescription> */}
        </CardHeader>
        <CardContent className="space-y-6">
          <Separator />

          {/* Action Buttons */}
          <div className="space-y-4">
            <div className="relative">
              <Button className="w-full bg-red-600 text-white hover:bg-red-700" size="lg">
                <MessageCircle className="mr-2 h-4 w-4" />
                Liên hệ môi giới
              </Button>
              <Badge className="absolute -top-2 -right-2 border-transparent bg-gradient-to-r from-indigo-500 to-pink-500 [background-size:105%] bg-center text-white">
                Coming Soon
              </Badge>
            </div>
            <div className="relative">
              <Button
                className="w-full bg-transparent"
                size="lg"
                variant="outline"
                onClick={onShowAppointment}
              >
                <Clock className="mr-2 h-4 w-4" />
                Đặt lịch hẹn
              </Button>
            </div>
            {property.transactionType === TransactionType.FOR_SALE && (
              <div className="relative">
                <Button
                  className="w-full bg-transparent"
                  size="lg"
                  variant="outline"
                  onClick={() => {
                    console.log("Desktop: 'Tính toán khoản vay' button clicked");
                    onShowLoan();
                  }}
                >
                  <HandCoins className="mr-2 h-4 w-4" />
                  Tính toán khoản vay
                </Button>
              </div>
            )}

            {property.transactionType === TransactionType.FOR_RENT && (
              <div className="relative">
                <Button
                  className="w-full bg-transparent"
                  size="lg"
                  variant="outline"
                  onClick={onShowCalculationTool}
                >
                  <Calculator className="mr-2 h-4 w-4" />
                  Tính toán chi phí thuê nhà
                </Button>
              </div>
            )}
          </div>

          <Separator />

          {/* Agent Details */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg"> Liên hệ {property.saler.fullName}</h3>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                <span className="text-red-600 font-semibold">
                  {property.saler.fullName.charAt(0)}
                </span>
              </div>
              <div>
                <p className="font-medium">{property.saler.fullName}</p>
                <p className="text-sm text-muted-foreground"> Liên hệ môi giới</p>
              </div>
            </div>
            <Button className="w-full bg-transparent" variant="outline" size="sm">
              <Phone className="mr-2 h-4 w-4" />
              {property.saler.phoneNumber}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
