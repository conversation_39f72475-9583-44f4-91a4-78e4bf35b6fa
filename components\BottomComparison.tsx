import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import type { Property } from '@/lib/api/services/fetchProperty';
import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface BottomComparisonProps {
  selectedProperties: Property[];
  onRemove: (id: string) => void;
}

export default function BottomComparison({ selectedProperties, onRemove }: BottomComparisonProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const maxSlots = 5;
  const emptySlots = maxSlots - selectedProperties.length;

  if (selectedProperties.length === 0) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t shadow-lg">
      <div className="container mx-auto px-4 py-3">
        {!isCollapsed ? (
          <div className="flex items-center justify-center p-4 gap-3 w-full overflow-x-auto">
            {selectedProperties.map(property => (
              <Card key={property.id} className="relative flex-shrink-0 w-32 p-2">
                <button
                  onClick={() => onRemove(property.id)}
                  className="absolute -top-1 -right-1 bg-gray-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-gray-600"
                >
                  <X className="w-3 h-3" />
                </button>
                <div className="flex flex-col items-center gap-2">
                  <div className="relative w-12 h-12">
                    <Image
                      src={property.imageUrls[0] || '/placeholder.svg'}
                      alt={property.title}
                      fill
                      className="object-cover rounded"
                    />
                  </div>

                  <p className="text-xs text-center line-clamp-1 leading-tight">{property.title}</p>
                </div>
              </Card>
            ))}

            {Array.from({ length: emptySlots }).map((_, index) => (
              <Card
                key={`empty-${index}`}
                className="flex-shrink-0 w-32 h-24 border-2 border-dashed border-gray-300 flex flex-col items-center justify-center cursor-default"
              >
                <p className="text-xs text-gray-400 text-center px-2">Chọn thêm căn hộ</p>
              </Card>
            ))}

            {/* Action Buttons */}
            <div className="ml-10 flex flex-col items-center gap-2">
              <div className=" flex gap-2 items-center">
                <Button variant="outline" size="sm" onClick={() => setIsCollapsed(true)}>
                  Thu gọn
                </Button>
                {selectedProperties.length >= 2 ? (
                  <Link
                    href={`/comparison/${selectedProperties.map(p => p.id).join('-vs-')}`}
                    target="_blank"
                    className="bg-red-600 hover:bg-red-700 text-white text-sm rounded-md px-3 py-1.5"
                  >
                    So sánh
                  </Link>
                ) : (
                  <Button size="sm" className="bg-red-600 hover:bg-red-700" disabled>
                    So sánh
                  </Button>
                )}
              </div>
              <div className="text-sm text-gray-600">
                Đã chọn {selectedProperties.length} sản phẩm
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <div className="text-sm text-gray-600">
                Đã chọn {selectedProperties.length} sản phẩm
              </div>
              <div className="flex -space-x-2">
                {selectedProperties.slice(0, 3).map((property, index) => (
                  <div
                    key={property.id}
                    className="relative w-8 h-8 rounded-full overflow-hidden border-2 border-white"
                    style={{ zIndex: 3 - index }}
                  >
                    <Image
                      src={property.imageUrls[0] || '/placeholder.svg'}
                      alt={property.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                ))}

                {selectedProperties.length > 3 && (
                  <div className="w-8 h-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-xs font-medium">
                    +{selectedProperties.length - 3}
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => setIsCollapsed(false)}>
                Mở rộng
              </Button>
              {selectedProperties.length >= 2 ? (
                <Link
                  href={`/comparison/${selectedProperties.map(p => p.id).join('-vs-')}`}
                  target="_blank"
                  className="bg-red-600 hover:bg-red-700 text-white text-sm rounded-md px-3 py-1.5"
                >
                  So sánh
                </Link>
              ) : (
                <Button size="sm" className="bg-red-600 hover:bg-red-700" disabled>
                  So sánh
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
