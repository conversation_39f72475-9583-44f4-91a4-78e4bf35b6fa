// components/DealLogs.tsx
'use client';
import React, { useState } from 'react';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { TooltipProvider } from '@/components/ui/tooltip';
import {
  Loader2,
  Calendar,
  User,
  ArrowRight,
  Trash2,
  Filter,
  Search,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';

import { DealLogSearchParams } from '@/lib/api/services/fetchDeal';
import { statusConfig } from '../../config/configuration';
import { UserInfoTooltip } from '@/components/UserInfoTooltip';
import { useDealLogs } from '@/hooks/useDeals';

interface DealLogsProps {
  dealId: string;
  className?: string;
}

export default function DealLogs({ dealId, className = '' }: DealLogsProps) {
  const [params, setParams] = useState<DealLogSearchParams>({
    pageNumber: 1,
    pageSize: 10,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const { logs, isLoading, isError, count, page, totalPages, refetch, isFetching } = useDealLogs(
    dealId,
    params
  );

  const handlePageChange = (newPage: number) => {
    setParams(prev => ({ ...prev, pageNumber: newPage }));
  };

  const handleFilterChange = (
    key: keyof DealLogSearchParams,
    value: string | number | undefined
  ) => {
    setParams(prev => ({
      ...prev,
      [key]: value,
      pageNumber: 1,
    }));
  };

  const handleSearch = () => {
    setParams(prev => ({
      ...prev,
      triggeredBy: searchTerm || undefined,
      pageNumber: 1,
    }));
  };

  const clearFilters = () => {
    setParams({
      pageNumber: 1,
      pageSize: 10,
    });
    setSearchTerm('');
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  };

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      className: 'bg-gray-100 text-gray-800',
    };
    return (
      <Badge className={`${config.className} whitespace-nowrap flex-shrink-0`}>
        {config.label}
      </Badge>
    );
  };

  if (isLoading && !logs.length) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center gap-2">
          <Loader2 className="h-5 w-5 animate-spin" />
          <span className="text-sm text-gray-600">Loading deal history...</span>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className={`p-6 text-center ${className}`}>
        <p className="text-red-500 mb-3 text-sm">Failed to load deal history</p>
        <Button size="sm" variant="outline" onClick={() => refetch()}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className={`space-y-4 ${className}`}>
        {/* Header with filters */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filters
            </Button>
            <Button variant="ghost" size="sm" onClick={() => refetch()} disabled={isFetching}>
              <RefreshCw className={`h-4 w-4 ${isFetching ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <Card className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Search by triggered by */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Search User</label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Search by user..."
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    className="flex-1"
                  />
                  <Button size="sm" onClick={handleSearch}>
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Status</label>
                <Select
                  value={params.status || ''}
                  onValueChange={value => handleFilterChange('status', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    {Object.entries(statusConfig).map(([key, config]) => (
                      <SelectItem key={key} value={key}>
                        {config.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Page Size */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Items per page</label>
                <Select
                  value={params.pageSize?.toString() || '10'}
                  onValueChange={value => handleFilterChange('pageSize', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end mt-4">
              <Button variant="outline" size="sm" onClick={clearFilters}>
                Clear Filters
              </Button>
            </div>
          </Card>
        )}

        {/* Logs List */}
        {logs.length === 0 ? (
          <Card className="p-8 text-center">
            <div className="text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium mb-2">No history found</p>
              <p className="text-sm">This deal doesn&apos;t have any activity logs yet.</p>
            </div>
          </Card>
        ) : (
          <div className="space-y-3 overflow-x-auto">
            {logs.map(log => (
              <Card key={log.id} className="p-4 hover:shadow-md transition-shadow min-w-fit">
                <div className="flex items-start justify-between min-w-[470px]">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-2 overflow-x-auto">
                      {log.isDeleteAction ? (
                        <div className="flex items-center gap-2 min-w-fit">
                          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <Trash2 className="h-4 w-4 text-red-600" />
                          </div>
                          <Badge variant="destructive" className="text-xs whitespace-nowrap">
                            DELETED
                          </Badge>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2 min-w-fit">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <ArrowRight className="h-4 w-4 text-blue-600" />
                          </div>
                          <div className="flex items-center gap-2 overflow-x-auto">
                            {getStatusBadge(log.fromStatus)}
                            <ArrowRight className="h-3 w-3 text-gray-400 flex-shrink-0" />
                            {getStatusBadge(log.toStatus)}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="ml-10">
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                        <User className="h-3 w-3 flex-shrink-0" />
                        <UserInfoTooltip
                          userInfo={{
                            avatar:
                              log.triggeredBy.avatar === null
                                ? log.triggeredBy.avatar
                                : log.triggeredBy.fullName,
                            name: log.triggeredBy.fullName,
                            role: log.triggeredBy.role,
                            email: log.triggeredBy.email,
                            phone: log.triggeredBy.phoneNumber,
                          }}
                        >
                          <span className="font-medium whitespace-nowrap hover:text-blue-600 hover:underline cursor-pointer transition-colors">
                            {log.triggeredBy.fullName}
                          </span>
                        </UserInfoTooltip>
                      </div>

                      {!log.isDeleteAction && (
                        <p className="text-sm text-gray-700 whitespace-nowrap">
                          Changed status from <span className="font-medium">{log.fromStatus}</span>{' '}
                          to <span className="font-medium">{log.toStatus}</span>
                        </p>
                      )}

                      {log.isDeleteAction && (
                        <p className="text-sm text-red-600 font-medium whitespace-nowrap">
                          Deal was deleted
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="text-xs text-gray-500 flex items-center gap-1 flex-shrink-0 ml-4">
                    <Calendar className="h-3 w-3" />
                    <span className="whitespace-nowrap">{formatDate(log.timestamp)}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Showing {(page - 1) * (params.pageSize || 10) + 1} to{' '}
              {Math.min(page * (params.pageSize || 10), count)} of {count} entries
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(page - 1)}
                disabled={page <= 1 || isFetching}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = i + 1;
                  return (
                    <Button
                      key={pageNum}
                      variant={page === pageNum ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handlePageChange(pageNum)}
                      disabled={isFetching}
                      className="w-8 h-8 p-0"
                    >
                      {pageNum}
                    </Button>
                  );
                })}

                {totalPages > 5 && (
                  <>
                    <span className="text-gray-500">...</span>
                    <Button
                      variant={page === totalPages ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handlePageChange(totalPages)}
                      disabled={isFetching}
                      className="w-8 h-8 p-0"
                    >
                      {totalPages}
                    </Button>
                  </>
                )}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(page + 1)}
                disabled={page >= totalPages || isFetching}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}
