import { CalendarProvider } from '@/app/(user)/appointments/components/calendar/calendar-context';
import { CalendarMain } from '@/app/(user)/appointments/components/calendar/calendar-main';
import { SecondarySidebar } from '@/app/(user)/appointments/components/secondary-sidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';

export default function Page() {
  return (
    <CalendarProvider>
      <div className="flex-1 flex flex-col overflow-hidden overflow-y-auto">
        <SidebarInset>
          <header className="sticky top-0 flex h-16 shrink-0 items-center gap-2 border-b bg-background px-4 z-10">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbPage>Calendar</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </header>

          <div className="flex flex-1 gap-4 p-4">
            {/* Sidebar phụ */}
            <div className="w-64 shrink-0">
              <div className="h-full border rounded-lg bg-background">
                <SecondarySidebar />
              </div>
            </div>

            {/* Main Calendar Content */}
            <div className="flex-1">
              <CalendarMain />
            </div>
          </div>
        </SidebarInset>
      </div>
    </CalendarProvider>
  );
}
