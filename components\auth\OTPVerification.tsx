'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useVerifyOtp, useVerifyOtpForLogin, useRequestOtp } from '@/hooks/useAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import InputOTPCustom from '@/components/customized/input-otp/input-otp';
import { AlertCircle, Mail, RefreshCw, X } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { maskEmail } from '@/utils/email/maskEmail';
import { useOtpErrorHandler } from '@/hooks/useAuth';

interface OTPVerificationProps {
  keyRegister: string;
  onClose?: () => void;
  onVerificationSuccess?: () => void;
}

export function OTPVerification({
  keyRegister,
  onClose,
  onVerificationSuccess,
}: OTPVerificationProps) {
  const router = useRouter();
  const [otp, setOtp] = useState('');
  const { error, setError, handleError } = useOtpErrorHandler();

  // Call both hooks to satisfy React Hook rules
  const verifyOtpForLogin = useVerifyOtpForLogin();
  const verifyOtpForRegister = useVerifyOtp();
  const { requestOtp, isLoading: isRequesting } = useRequestOtp();

  // Use different hooks based on context
  const isLoginFlow = !!onVerificationSuccess;
  const { verifyOtp, isLoading: isVerifying } = isLoginFlow
    ? verifyOtpForLogin
    : verifyOtpForRegister;

  // Redirect if no email parameter (only for register flow)
  useEffect(() => {
    if (!keyRegister && !isLoginFlow) {
      router.push('/register');
    }
  }, [keyRegister, router, isLoginFlow]);

  // Clear errors when OTP changes
  useEffect(() => {
    if (otp.length > 0) {
      setError(null);
    }
  }, [otp, setError]);

  const handleVerifyOtp = (e: React.FormEvent) => {
    e.preventDefault();

    if (otp.length !== 6) {
      setError('Vui lòng nhập đầy đủ 6 số OTP');
      return;
    }

    setError(null);
    verifyOtp(
      { verifyKey: keyRegister, otp },
      {
        onSuccess: (_data: object) => {
          if (onVerificationSuccess) {
            onVerificationSuccess();
          }
        },
        onError: handleError,
      }
    );
  };

  const handleResendOtp = async () => {
    if (!keyRegister) return;

    await requestOtp({
      verifyKey: keyRegister,
    });

    // Clear OTP input
    setOtp('');
  };

  const handleOtpChange = (value: string) => {
    setOtp(value);
  };

  const isOtpExpired =
    error?.includes('Mã OTP không hợp lệ') ||
    error?.includes('OTP không hợp lệ') ||
    error?.includes('OTP thất bại');

  if (!keyRegister) {
    return null; // Will redirect in useEffect for register flow
  }

  return (
    <div className="flex items-center justify-center p-4">
      <Card className="w-full max-w-md border-none shadow-none">
        <CardHeader className="space-y-1 text-center relative">
          <div className="flex justify-center mb-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
              <Mail className="h-6 w-6 text-primary" color="#ff0000" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-primary">Xác thực OTP</CardTitle>
          <CardDescription className="text-center text-muted-foreground">
            Nhập mã OTP đã được gửi đến email hoặc số điện thoại của bạn
          </CardDescription>
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="absolute top-2 right-2 h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">Mã OTP đã được gửi đến</p>
            <Badge
              variant="outline"
              className="font-mono text-sm border-primary text-primary bg-primary/5"
            >
              {maskEmail(keyRegister)}
            </Badge>
          </div>

          {/* OTP Form */}
          <form onSubmit={handleVerifyOtp} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="otp" className="text-center block">
                Nhập mã OTP
              </Label>
              <InputOTPCustom
                value={otp}
                onChange={handleOtpChange}
                maxLength={6}
                disabled={isVerifying}
                autoFocus
                className="flex justify-center"
              />
            </div>

            {/* Error Messages */}
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {error}
                  {isOtpExpired && (
                    <div className="mt-2 text-sm">
                      Mã OTP có thể đã hết hạn. Vui lòng yêu cầu gửi lại mã OTP mới.
                    </div>
                  )}
                </AlertDescription>
              </Alert>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full bg-primary text-white hover:bg-primary/90 transition"
              disabled={otp.length !== 6 || isVerifying}
            >
              {isVerifying ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Đang xác thực
                </>
              ) : (
                'Xác thực'
              )}
            </Button>
          </form>

          {/* Resend Section */}
          <div className="text-center space-y-3">
            <p className="text-sm text-muted-foreground">
              {isOtpExpired ? 'Mã OTP đã hết hạn hoặc không hợp lệ. ' : ''}
              Không nhận được mã OTP?
            </p>

            <Button
              variant="outline"
              onClick={handleResendOtp}
              disabled={isRequesting}
              className="w-full border-primary text-primary hover:bg-primary/10 transition"
            >
              {isRequesting ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Đang gửi mã OTP
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Gửi lại mã OTP
                </>
              )}
            </Button>

            <p className="text-xs text-muted-foreground">
              Kiểm tra thư rác nếu không nhận được mã OTP
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
