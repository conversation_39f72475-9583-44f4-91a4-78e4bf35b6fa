import React from 'react';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useRouter } from 'next/navigation';
import { Eye, Loader2, MoreHorizontal, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

import { Deal, DealSearchParams } from '@/lib/api/services/fetchDeal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { DeleteDealDialog } from '@/app/(admin)/admin/sales/components/DeleteDealDialog';
import { priorityConfig, statusConfig } from '../config/configuration';
import { useDeleteDealWithConfirm } from '../custom-hooks/useDeleteDealWithConfirm';

interface DealTableProps {
  data: { deals: Deal[]; count: number } | undefined;
  isLoading: boolean;
  error: Error | null;
  filters: DealSearchParams;
  pagination: { pageNumber: number; pageSize: number };
  handleFilterChange: (key: keyof DealSearchParams, value: string | boolean) => void;
  handlePageChange: (page: number) => void;
  handlePageSizeChange: (size: number) => void;
}

export default function DealTable({
  data,
  isLoading,
  error,
  pagination,
  handlePageChange,
}: DealTableProps) {
  const router = useRouter();
  const { dealToDelete, handleDeleteClick, confirmDelete, closeDeleteDialog, isDeleting } =
    useDeleteDealWithConfirm();

  // Handle view deal details
  const handleViewDealDetails = (dealId: string) => {
    router.push(`sales/${dealId}`);
  };

  // Calculate total pages
  const totalPages = Math.ceil((data?.count || 0) / pagination.pageSize);

  if (isLoading) {
    return (
      <Card className="w-full p-8">
        <div className="flex items-center justify-center">
          <p className="text-muted-foreground">Loading deals...</p>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full p-8">
        <div className="flex items-center justify-center">
          <p className="text-red-500">Error loading deals. Please try again.</p>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-4 bg-gray-50 min-h-screen p-4">
      {/* Deals Table */}
      <Card className="w-full">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">Priority</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead>Updated At</TableHead>
              <TableHead className="w-[80px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data?.deals.map((deal: Deal) => (
              <TableRow
                key={deal.id}
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleViewDealDetails(deal.id)}
              >
                <TableCell>
                  <Badge
                    className={
                      deal.priority &&
                      priorityConfig.list[
                        deal.priority.toUpperCase() as keyof typeof priorityConfig.list
                      ]
                        ? priorityConfig.list[
                            deal.priority.toUpperCase() as keyof typeof priorityConfig.list
                          ].className
                        : 'bg-gray-100 text-gray-800'
                    }
                  >
                    {deal.priority &&
                    priorityConfig.list[
                      deal.priority.toUpperCase() as keyof typeof priorityConfig.list
                    ]
                      ? priorityConfig.list[
                          deal.priority.toUpperCase() as keyof typeof priorityConfig.list
                        ].label
                      : deal.priority || 'N/A'}
                  </Badge>
                </TableCell>
                <TableCell className="font-medium">{deal.title || 'Untitled'}</TableCell>
                <TableCell>
                  {deal.description && deal.description.length > 30
                    ? `${deal.description.substring(0, 30)}...`
                    : deal.description || 'No description'}
                </TableCell>
                <TableCell>{deal.customer?.name || 'N/A'}</TableCell>
                <TableCell>
                  <Badge
                    className={
                      deal.status && statusConfig[deal.status as keyof typeof statusConfig]
                        ? statusConfig[deal.status as keyof typeof statusConfig].className
                        : 'bg-gray-100 text-gray-800'
                    }
                  >
                    {deal.status && statusConfig[deal.status as keyof typeof statusConfig]
                      ? statusConfig[deal.status as keyof typeof statusConfig].label
                      : deal.status || 'N/A'}
                  </Badge>
                </TableCell>
                <TableCell>
                  {deal.createdAt ? format(new Date(deal.createdAt), 'MMM dd, yyyy HH:mm') : 'N/A'}
                </TableCell>
                <TableCell>
                  {deal.updatedAt ? format(new Date(deal.updatedAt), 'MMM dd, yyyy HH:mm') : 'N/A'}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={e => {
                        e.stopPropagation();
                        handleViewDealDetails(deal.id);
                      }}
                      disabled={isDeleting}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>

                    <DropdownMenu modal={false}>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={e => e.stopPropagation()}
                          disabled={isDeleting}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem
                          onClick={e => {
                            e.stopPropagation();
                            handleViewDealDetails(deal.id);
                          }}
                          disabled={isDeleting}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>

                        <DropdownMenuSeparator />

                        <DropdownMenuItem
                          onClick={e => handleDeleteClick(deal, e)}
                          className="text-red-600 focus:text-red-600 focus:bg-red-50"
                          disabled={isDeleting}
                        >
                          {isDeleting ? (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="mr-2 h-4 w-4" />
                          )}
                          {isDeleting ? 'Deleting...' : 'Delete Deal'}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => handlePageChange(pagination.pageNumber - 1)}
                  className={
                    pagination.pageNumber === 1
                      ? 'pointer-events-none opacity-50'
                      : 'cursor-pointer'
                  }
                />
              </PaginationItem>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                <PaginationItem key={page}>
                  <PaginationLink
                    onClick={() => handlePageChange(page)}
                    isActive={pagination.pageNumber === page}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  onClick={() => handlePageChange(pagination.pageNumber + 1)}
                  className={
                    pagination.pageNumber === totalPages
                      ? 'pointer-events-none opacity-50'
                      : 'cursor-pointer'
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      <DeleteDealDialog
        deal={dealToDelete}
        open={!!dealToDelete}
        onOpenChange={closeDeleteDialog}
        onConfirm={confirmDelete}
        isDeleting={isDeleting}
      />
    </div>
  );
}
