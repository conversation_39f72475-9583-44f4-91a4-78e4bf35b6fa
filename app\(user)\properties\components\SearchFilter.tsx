import { useState, useEffect } from 'react';
import {
  MapPin,
  DollarSign,
  Ruler,
  Building,
  Home,
  ChevronDown,
  X,
  Building2,
  LandPlot,
  Store,
  KeyRound,
  BedDouble,
  CircleCheck,
  WalletCards,
  SearchIcon,
  Loader2,
  XIcon,
  SlidersHorizontal,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuGroup,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '@/components/ui/dropdown-menu';

import { Sheet, SheetContent, SheetTrigger, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Separator } from '@/components/ui/separator';
import { useRouter, useSearchParams } from 'next/navigation';
import { TransactionType } from '@/lib/api/services/fetchProperty';
import { Slider } from '@/components/ui/slider';
import { formatCurrency } from '@/utils/numbers/formatCurrency';
import Image from 'next/image';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import * as RadioGroup from '@radix-ui/react-radio-group';
import { AreaChart, Area, ResponsiveContainer, XAxis, YAxis, Tooltip } from 'recharts';
import * as CheckboxPrimitive from '@radix-ui/react-checkbox';
// Define interfaces for location data
interface Province {
  code: number;
  name: string;
  districts?: District[];
}

interface District {
  code: number;
  name: string;
  wards?: Ward[];
}

interface Ward {
  code: number;
  name: string;
}

// Define the filter types with proper typing
interface FilterValues {
  city?: string;
  district?: string;
  ward?: string;
  propertyType?: string;
  transactionType?: TransactionType;
  priceRange?: string;
  propertySize?: string;
  buildYear?: string;
  bedCount?: string;
  bathCount?: string;
  exactBedMatch?: string;
  bedCountDisplay?: string;
  bathCountDisplay?: string;
  [key: string]: string | undefined;
}

// Property type mapping to API enum values
// const propertyTypeMap: Record<string, PropertyType> = {
//   'apartment': PropertyType.APARTMENT,
//   'land_plot': PropertyType.LAND_PLOT,
//   'villa': PropertyType.VILLA,
//   'shop_house': PropertyType.SHOP_HOUSE,
// };

// Add these constants at the top of the file
const CENTRAL_CITIES = [
  {
    code: '1',
    name: 'Hà Nội',
    image:
      'https://www.atlys.com/_next/image?url=https%3A%2F%2Fimagedelivery.net%2FW3Iz4WACAy2J0qT0cCT3xA%2Fdidi%2Farticles%2Frlopvfixv5sap0yupj9osxaa%2Fpublic&w=1920&q=75',
    icon: '🏛️',
  },
  {
    code: '79',
    name: 'TP. Hồ Chí Minh',
    image:
      'https://media.vneconomy.vn/images/upload/2024/05/20/tphcm-16726501373541473396704-16994302498261147920222.jpg',
    icon: '🌆',
  },
  {
    code: '48',
    name: 'Đà Nẵng',
    image:
      'https://vcdn1-dulich.vnecdn.net/2022/06/03/cauvang-1654247842-9403-1654247849.jpg?w=1200&h=0&q=100&dpr=1&fit=crop&s=Swd6JjpStebEzT6WARcoOA',
    icon: '🏖️',
  },
  {
    code: '92',
    name: 'Cần Thơ',
    image: 'https://ik.imagekit.io/tvlk/blog/2021/11/dia-diem-du-lich-can-tho-cover.jpg',
    icon: '🌾',
  },
  {
    code: '31',
    name: 'Hải Phòng',
    image: 'https://heza.gov.vn/wp-content/uploads/2023/09/hai_phong-scaled.jpg',
    icon: '⚓',
  },
];

export default function SearchFilter() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeFilters, setActiveFilters] = useState<FilterValues>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  // Location states
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [districts, setDistricts] = useState<District[]>([]);
  const [wards, setWards] = useState<Ward[]>([]);
  const [loading, setLoading] = useState(false);
  console.log(loading);
  const [selectedProvince, setSelectedProvince] = useState<string>('');
  const [selectedDistrict, setSelectedDistrict] = useState<string>('');
  const [selectedWard, setSelectedWard] = useState<string>('');
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const [sliderValue, setSliderValue] = useState([0, Infinity]);

  const [selectedPropertyTypes, setSelectedPropertyTypes] = useState<string[]>([]);
  const [isPropertyTypeOpen, setIsPropertyTypeOpen] = useState(false);
  console.log(isPropertyTypeOpen);
  const [selectedTransactionType, setSelectedTransactionType] = useState<string>('');
  const [isTransactionTypeOpen, setIsTransactionTypeOpen] = useState(false);

  const [selectedBedCount, setSelectedBedCount] = useState<string>('any');
  const [selectedBathCount, setSelectedBathCount] = useState<string>('any');
  const [isExactBedMatch, setIsExactBedMatch] = useState(false);
  const [isRoomsDropdownOpen, setIsRoomsDropdownOpen] = useState(false);

  const [priceDistribution] = useState({
    sale: [
      { price: '0-1', count: 120 },
      { price: '1-2', count: 250 },
      { price: '2-3', count: 180 },
      { price: '3-4', count: 90 },
      { price: '4-5', count: 60 },
      { price: '5-6', count: 40 },
      { price: '6-7', count: 30 },
      { price: '7-8', count: 20 },
      { price: '8-9', count: 15 },
      { price: '9-10', count: 10 },
    ],
    rent: [
      { price: '0-5', count: 150 },
      { price: '5-10', count: 280 },
      { price: '10-15', count: 320 },
      { price: '15-20', count: 250 },
      { price: '20-25', count: 180 },
      { price: '25-30', count: 120 },
      { price: '30-40', count: 80 },
      { price: '40-50', count: 50 },
      { price: '50-70', count: 30 },
      { price: '70+', count: 20 },
    ],
  });

  const quickPriceRanges = {
    sale: [
      { label: 'Dưới 1 tỷ', value: [0, 1000000000] },
      { label: '1-2 tỷ', value: [1000000000, 2000000000] },
      { label: '2-3 tỷ', value: [2000000000, 3000000000] },
      { label: '3-5 tỷ', value: [3000000000, 5000000000] },
      { label: '5-7 tỷ', value: [5000000000, 7000000000] },
      { label: 'Trên 7 tỷ', value: [7000000000, 10000000000] },
    ],
    rent: [
      { label: 'Dưới 5tr', value: [0, 5000000] },
      { label: '5-10tr', value: [5000000, 10000000] },
      { label: '10-15tr', value: [10000000, 15000000] },
      { label: '15-20tr', value: [15000000, 20000000] },
      { label: '20-30tr', value: [20000000, 30000000] },
      { label: 'Trên 30tr', value: [30000000, 100000000] },
    ],
  };

  interface QuickRange {
    label: string;
    value: [number, number];
  }

  const quickSizeRanges: QuickRange[] = [
    { label: 'Dưới 30m²', value: [0, 30] },
    { label: '30-50m²', value: [30, 50] },
    { label: '50-70m²', value: [50, 70] },
    { label: '70-100m²', value: [70, 100] },
    { label: '100-150m²', value: [100, 150] },
    { label: 'Trên 150m²', value: [150, 1000] },
  ];

  const getCurrentPriceRanges = () => {
    const isRent = activeFilters.transactionType === TransactionType.FOR_RENT;
    return {
      distribution: priceDistribution[isRent ? 'rent' : 'sale'],
      quickRanges: quickPriceRanges[isRent ? 'rent' : 'sale'],
      maxValue: isRent ? 100000000 : 10000000000, // 100M for rent, 10B for sale
      step: isRent ? 100000 : 1000000, // 100K for rent, 1M for sale
    };
  };

  const formatPriceLabel = (value: number, isMax: boolean = false) => {
    const isRent = activeFilters.transactionType === TransactionType.FOR_RENT;
    if (isRent) {
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)} triệu${isMax ? '+' : ''}/tháng`;
      }
      if (value >= 1000) {
        return `${(value / 1000).toFixed(1)} nghìn${isMax ? '+' : ''}/tháng`;
      }
      return `${value}${isMax ? '+' : ''}/tháng`;
    } else {
      if (value >= 1000000000) {
        return `${(value / 1000000000).toFixed(1)} tỷ${isMax ? '+' : ''}`;
      }
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)} triệu${isMax ? '+' : ''}`;
      }
      if (value >= 1000) {
        return `${(value / 1000).toFixed(1)} nghìn${isMax ? '+' : ''}`;
      }
      return `${value}${isMax ? '+' : ''}`;
    }
  };

  const propertyTypes = [
    { id: 'apartment', label: 'Chung cư', icon: Building2 },
    { id: 'villa', label: 'Biệt thự', icon: Home },
    { id: 'land_plot', label: 'Đất', icon: LandPlot },
    { id: 'shop_house', label: 'Nhà phố', icon: Store },
  ];

  const transactionTypes = [
    { id: 'both', label: 'Tất cả', icon: WalletCards },
    { id: TransactionType.FOR_SALE, label: 'Bán', icon: DollarSign },
    { id: TransactionType.FOR_RENT, label: 'Cho thuê', icon: KeyRound },
  ];

  const bedOptions = [
    { id: 'any', label: 'Bất kỳ' },
    { id: '1+', label: '1+' },
    { id: '2+', label: '2+' },
    { id: '3+', label: '3+' },
    { id: '4+', label: '4+' },
    { id: '5+', label: '5+' },
  ];

  const exactBedOptions = [
    { id: 'studio', label: 'Studio' },
    { id: '1', label: '1' },
    { id: '2', label: '2' },
    { id: '3', label: '3' },
    { id: '4', label: '4' },
    { id: '5', label: '5' },
  ];

  const bathOptions = [
    { id: 'any', label: 'Bất kỳ' },
    { id: '1+', label: '1+' },
    { id: '2+', label: '2+' },
    { id: '3+', label: '3+' },
    { id: '4+', label: '4+' },
    { id: '5+', label: '5+' },
  ];

  const handleApply = () => {
    const priceRangeString = `${formatCurrency(sliderValue[0], 'VND')} đến ${formatCurrency(sliderValue[1], 'VND')}`;
    handleFilterChange('priceRange', priceRangeString);
    setIsPriceRangeOpen(false);
  };

  const handleReset = () => {
    setSliderValue([0, getCurrentPriceRanges().maxValue]);
    setMinPrice('');
    setMaxPrice('');
    handleFilterChange('priceRange', '');
  };

  const formatPriceDisplay = (priceRange: string) => {
    if (!priceRange) return 'Mức giá';

    const isRent = activeFilters.transactionType === TransactionType.FOR_RENT;
    const [min, max] = priceRange.split(' đến ').map(price => {
      const value = price.replace(/[^0-9]/g, '');
      const numValue = parseInt(value);

      if (isRent) {
        if (numValue >= 1000000) {
          return `${(numValue / 1000000).toFixed(1)} triệu/tháng`;
        }
        return `${(numValue / 1000).toFixed(1)} nghìn/tháng`;
      } else {
        if (numValue >= 1000000000) {
          return `${(numValue / 1000000000).toFixed(1)} tỷ`;
        }
        return `${(numValue / 1000000).toFixed(1)} triệu`;
      }
    });

    return `${min} - ${max}`;
  };

  // Initialize filters from URL search params
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    const initialFilters: FilterValues = {};

    // Extract filters from URL
    if (params.has('city')) initialFilters.city = params.get('city') || undefined;
    if (params.has('district')) initialFilters.district = params.get('district') || undefined;
    if (params.has('ward')) initialFilters.ward = params.get('ward') || undefined;
    if (params.has('propertyType'))
      initialFilters.propertyType = params.get('propertyType') || undefined;
    if (params.has('transactionType')) {
      const transactionType = params.get('transactionType');
      if (
        transactionType === TransactionType.FOR_SALE ||
        transactionType === TransactionType.FOR_RENT
      ) {
        initialFilters.transactionType = transactionType;
      }
    }

    // Initialize price range
    if (params.has('minPrice') || params.has('maxPrice')) {
      const minPrice = Number(params.get('minPrice')) || 0;
      const maxPrice = Number(params.get('maxPrice')) || getCurrentPriceRanges().maxValue;
      setSliderValue([minPrice, maxPrice]);
      setMinPrice(minPrice.toString());
      setMaxPrice(maxPrice.toString());
      // Set the price range in activeFilters
      initialFilters.priceRange = `${formatCurrency(minPrice, 'VND')} đến ${formatCurrency(maxPrice, 'VND')}`;
    }

    // Initialize area range
    if (params.has('minArea') || params.has('maxArea')) {
      const minArea = Number(params.get('minArea')) || 0;
      const maxArea = Number(params.get('maxArea')) || 1000;
      setSizeSliderValue([minArea, maxArea]);
      setMinSize(minArea.toString());
      setMaxSize(maxArea.toString());
      // Set the area range in activeFilters
      initialFilters.propertySize = `${formatSizeLabel(minArea)} đến ${formatSizeLabel(maxArea)}`;
    }

    // Initialize bed count
    if (params.has('bedCount')) {
      const bedCount = params.get('bedCount') || 'any';
      setSelectedBedCount(bedCount);
      setIsExactBedMatch(params.get('exactBedMatch') === 'true');

      // Set the bed count display in activeFilters
      if (bedCount !== 'any') {
        const isExact = params.get('exactBedMatch') === 'true';
        const displayText = isExact
          ? bedCount === 'studio'
            ? 'Studio'
            : `${bedCount} phòng ngủ`
          : `${bedCount} phòng ngủ`;
        initialFilters.bedCountDisplay = displayText;
      }
    }

    // Initialize bath count
    if (params.has('bathCount')) {
      const bathCount = params.get('bathCount') || 'any';
      setSelectedBathCount(bathCount);

      // Set the bath count display in activeFilters
      if (bathCount !== 'any') {
        initialFilters.bathCountDisplay = `${bathCount} phòng tắm`;
      }
    }

    // Set initial search query if present
    if (params.has('searchTerm')) {
      setSearchQuery(params.get('searchTerm') || '');
    }

    setActiveFilters(initialFilters);
  }, [searchParams]);

  // Fetch provinces on component mount
  useEffect(() => {
    const fetchProvinces = async () => {
      setLoading(true);
      try {
        const response = await fetch('https://provinces.open-api.vn/api/p/');
        if (!response.ok) throw new Error('Failed to fetch provinces');
        const data = await response.json();
        setProvinces(data);
      } catch (error) {
        console.error('Error fetching provinces:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProvinces();
  }, []);

  // Fetch districts when province changes
  useEffect(() => {
    if (!selectedProvince) {
      setDistricts([]);
      return;
    }

    const fetchDistricts = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `https://provinces.open-api.vn/api/p/${selectedProvince}?depth=2`
        );
        if (!response.ok) throw new Error('Failed to fetch districts');
        const data = await response.json();
        setDistricts(data.districts || []);
      } catch (error) {
        console.error('Error fetching districts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDistricts();
  }, [selectedProvince]);

  // Fetch wards when district changes
  useEffect(() => {
    if (!selectedDistrict) {
      setWards([]);
      return;
    }

    const fetchWards = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `https://provinces.open-api.vn/api/d/${selectedDistrict}?depth=2`
        );
        if (!response.ok) throw new Error('Failed to fetch wards');
        const data = await response.json();
        setWards(data.wards || []);
      } catch (error) {
        console.error('Error fetching wards:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWards();
  }, [selectedDistrict]);

  const handleFilterChange = (category: string, value: string) => {
    setActiveFilters(
      prev =>
        ({
          ...prev,
          [category]: value,
        }) as FilterValues
    );
  };

  const handleLocationChange = (type: 'province' | 'district' | 'ward', value: string) => {
    if (type === 'province') {
      const province = provinces.find(p => p.code.toString() === value);
      setSelectedProvince(value);
      setSelectedDistrict('');
      setSelectedWard('');
      if (province?.name) {
        handleFilterChange('city', province.name);
        // Clear district and ward from activeFilters
        handleFilterChange('district', '');
        handleFilterChange('ward', '');
      } else {
        handleFilterChange('city', '');
      }
    } else if (type === 'district') {
      const district = districts.find(d => d.code.toString() === value);
      setSelectedDistrict(value);
      setSelectedWard('');
      if (district?.name) {
        handleFilterChange('district', district.name);
        // Clear ward from activeFilters
        handleFilterChange('ward', '');
      } else {
        handleFilterChange('district', '');
      }
    } else {
      const ward = wards.find(w => w.code.toString() === value);
      setSelectedWard(value);
      if (ward?.name) {
        handleFilterChange('ward', ward.name);
      } else {
        handleFilterChange('ward', '');
      }
    }
  };

  const clearFilters = () => {
    setActiveFilters({});
    setSearchQuery('');
    setSelectedProvince('');
    setSelectedDistrict('');
    setSelectedWard('');
    setSliderValue([0, getCurrentPriceRanges().maxValue]);
    setSizeSliderValue([0, 1000]);
    setSelectedPropertyTypes([]);
    setSelectedTransactionType('');
    setSelectedBedCount('any');
    setSelectedBathCount('any');
    setIsExactBedMatch(false);
    setMinPrice('');
    setMaxPrice('');
    setMinSize('');
    setMaxSize('');

    // Clear URL parameters by navigating to the base URL
    router.push('/properties');
  };

  const countActiveFilters = () => {
    return Object.keys(activeFilters).filter(key => activeFilters[key]).length;
  };

  const handleSearch = () => {
    setIsSearching(true);

    // Build the URL query string from the filters
    const queryParams = new URLSearchParams();

    // Add search term if not empty
    if (searchQuery.trim()) {
      queryParams.set('searchTerm', searchQuery.trim());
    }

    // Add location parameters only if they have values
    if (activeFilters.city) {
      queryParams.set('city', activeFilters.city);
    }
    if (activeFilters.district) {
      queryParams.set('district', activeFilters.district);
    }
    if (activeFilters.ward) {
      queryParams.set('ward', activeFilters.ward);
    }

    // Add other active filters to URL
    Object.entries(activeFilters).forEach(([key, value]) => {
      if (
        value &&
        ![
          'city',
          'district',
          'ward',
          'priceRange',
          'propertySize',
          'bedCountDisplay',
          'bathCountDisplay',
        ].includes(key)
      ) {
        // Special handling for transaction type to ensure it's properly encoded
        if (key === 'transactionType') {
          queryParams.set(key, value as string);
        } else {
          queryParams.set(key, value);
        }
      }
    });

    // Add price range if set
    if (sliderValue[0] > 0 || sliderValue[1] < getCurrentPriceRanges().maxValue) {
      queryParams.set('minPrice', sliderValue[0].toString());
      queryParams.set('maxPrice', sliderValue[1].toString());
    }

    // Add area range if set
    if (sizeSliderValue[0] > 0 || sizeSliderValue[1] < 1000) {
      queryParams.set('minArea', sizeSliderValue[0].toString());
      queryParams.set('maxArea', sizeSliderValue[1].toString());
    }

    // Navigate to the same page with filters in URL
    router.push(`/properties?${queryParams.toString()}`);

    // Reset searching state after a short delay to show loading state
    setTimeout(() => {
      setIsSearching(false);
    }, 500);
  };

  const handlePropertyTypeChange = (type: string) => {
    setSelectedPropertyTypes(prev => {
      if (prev.includes(type)) {
        return prev.filter(t => t !== type);
      }
      return [...prev, type];
    });
  };

  const handleSelectAllPropertyTypes = () => {
    if (selectedPropertyTypes.length === propertyTypes.length) {
      setSelectedPropertyTypes([]);
    } else {
      setSelectedPropertyTypes(propertyTypes.map(type => type.id));
    }
  };

  const handleApplyPropertyTypes = () => {
    if (selectedPropertyTypes.length === 0) {
      handleFilterChange('propertyType', '');
    } else if (selectedPropertyTypes.length === 1) {
      handleFilterChange('propertyType', selectedPropertyTypes[0]);
    } else {
      handleFilterChange('propertyType', selectedPropertyTypes.join(','));
    }
    setIsPropertyTypeOpen(false);
  };

  const handleTransactionTypeChange = (value: string) => {
    setSelectedTransactionType(value);
  };

  const handleApplyTransactionType = () => {
    handleFilterChange(
      'transactionType',
      selectedTransactionType === 'both' ? '' : selectedTransactionType
    );
    setIsTransactionTypeOpen(false);
  };

  const handleBedCountChange = (value: string) => {
    setSelectedBedCount(value);
  };

  const handleBathCountChange = (value: string) => {
    setSelectedBathCount(value);
  };

  const handleApplyRooms = () => {
    // Handle bed count
    const bedValue = isExactBedMatch
      ? selectedBedCount === 'any'
        ? ''
        : selectedBedCount
      : selectedBedCount === 'any'
        ? ''
        : selectedBedCount;

    let bedDisplayText = '';
    if (selectedBedCount !== 'any') {
      if (isExactBedMatch) {
        bedDisplayText = selectedBedCount === 'studio' ? 'Studio' : `${selectedBedCount} phòng ngủ`;
      } else {
        bedDisplayText = `${selectedBedCount} phòng ngủ`;
      }
    }

    // Handle bath count
    const bathValue = selectedBathCount === 'any' ? '' : selectedBathCount;
    const bathDisplayText = selectedBathCount === 'any' ? '' : `${selectedBathCount} phòng tắm`;

    // Update filters
    handleFilterChange('bedCount', bedValue);
    handleFilterChange('exactBedMatch', isExactBedMatch.toString());
    handleFilterChange('bedCountDisplay', bedDisplayText);
    handleFilterChange('bathCount', bathValue);
    handleFilterChange('bathCountDisplay', bathDisplayText);

    setIsRoomsDropdownOpen(false);
  };

  // const formatBedDisplay = (display: string) => {
  //   if (!display) return 'Số phòng ngủ';
  //   return display;
  // };

  // const formatBathDisplay = (display: string) => {
  //   if (!display) return 'Số phòng tắm';
  //   return display;
  // };

  const formatSizeLabel = (value: number, isMax: boolean = false) => {
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)} nghìn${isMax ? '+' : ''} m²`;
    }
    return `${value}${isMax ? '+' : ''} m²`;
  };

  const [isPriceRangeOpen, setIsPriceRangeOpen] = useState(false);

  const [isPropertySizeOpen, setIsPropertySizeOpen] = useState(false);
  const [minSize, setMinSize] = useState('');
  const [maxSize, setMaxSize] = useState('');
  const [sizeSliderValue, setSizeSliderValue] = useState([0, 1000]);
  const [sizeDistribution] = useState([
    { size: '0-30', count: 120 },
    { size: '30-50', count: 280 },
    { size: '50-70', count: 320 },
    { size: '70-100', count: 250 },
    { size: '100-150', count: 180 },
    { size: '150-200', count: 120 },
    { size: '200-300', count: 80 },
    { size: '300-500', count: 50 },
    { size: '500-1000', count: 30 },
    { size: '1000+', count: 20 },
  ]);

  const handleApplySize = () => {
    const sizeRangeString = `${formatSizeLabel(sizeSliderValue[0])} đến ${formatSizeLabel(sizeSliderValue[1])}`;
    handleFilterChange('propertySize', sizeRangeString);
    setIsPropertySizeOpen(false);
  };

  const handleResetSize = () => {
    setSizeSliderValue([0, 1000]);
    setMinSize('');
    setMaxSize('');
    handleFilterChange('propertySize', '');
  };

  const formatSizeDisplay = (sizeRange: string) => {
    if (!sizeRange) return 'Diện tích';
    return sizeRange;
  };

  const [isMoreFiltersOpen, setIsMoreFiltersOpen] = useState(false);

  return (
    <section className="w-full max-w-screen mx-auto bg-background text-foreground font-mann">
      <div className="ml-4">
        <div className="w-full border-0 bg-card overflow-hidden">
          <div className="p-2 space-y-2">
            {/* Search Input - Full width on mobile */}
            {/* Filter Buttons - Responsive Grid */}
            <div className="flex flex-row gap-2">
              <div className="max-xl:hidden relative w-full md:w-[300px] flex-shrink-0">
                <Input
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  placeholder={'Tìm kiếm bất động sản...'}
                  className="text-base"
                />
                {searchQuery && (
                  <Button
                    size="icon"
                    variant="ghost"
                    className="absolute right-0 top-1/2 -translate-y-1/2 h-9 w-9 text-muted-foreground hover:text-foreground"
                    onClick={() => setSearchQuery('')}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {/* Location Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={`max-xl:hidden w-full focus-visible:ring-0 focus-visible:outline-none ${selectedProvince ? 'bg-red-50 border-red-200 hover:bg-red-100' : 'text-muted-foreground'}`}
                  >
                    <div className="flex items-center">
                      <MapPin
                        className={`${selectedProvince ? 'text-red-600' : 'text-muted-foreground'} mr-2 h-4 w-4`}
                      />
                      <span
                        className={`truncate ${selectedProvince ? 'text-red-600' : 'text-muted-foreground'}`}
                      >
                        {selectedProvince
                          ? provinces.find(p => p.code.toString() === selectedProvince)?.name
                          : 'Chọn địa điểm'}
                      </span>
                    </div>
                    <ChevronDown
                      className={`${selectedProvince ? 'text-red-600' : 'text-muted-foreground'} h-4 w-4 ml-2`}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[calc(100vw-2rem)] sm:w-[600px] md:w-[800px] lg:w-[1200px] p-4 font-mann">
                  <div className="space-y-6">
                    {/* Location Selection Tabs */}
                    <div className="flex gap-2 border-b">
                      <button
                        onClick={() => {
                          setSelectedProvince('');
                          setSelectedDistrict('');
                          setSelectedWard('');
                        }}
                        className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                          !selectedProvince
                            ? 'border-primary text-primary'
                            : 'border-transparent text-muted-foreground hover:text-foreground'
                        }`}
                      >
                        Tỉnh/Thành phố
                      </button>
                      <button
                        onClick={() => {
                          if (selectedProvince) {
                            setSelectedDistrict('');
                            setSelectedWard('');
                          }
                        }}
                        className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                          selectedProvince && !selectedDistrict
                            ? 'border-primary text-primary'
                            : 'border-transparent text-muted-foreground hover:text-foreground'
                        } ${!selectedProvince ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        Quận/Huyện
                      </button>
                      <button
                        onClick={() => {
                          if (selectedDistrict) {
                            setSelectedWard('');
                          }
                        }}
                        className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                          selectedDistrict && !selectedWard
                            ? 'border-primary text-primary'
                            : 'border-transparent text-muted-foreground hover:text-foreground'
                        } ${!selectedDistrict ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        Phường/Xã
                      </button>
                    </div>

                    {/* Location Selection Content */}
                    <div className="space-y-6">
                      {/* Province Selection */}
                      {!selectedProvince && (
                        <div className="space-y-6">
                          {/* Central Cities Section */}
                          <div className="space-y-4">
                            <h4 className="font-medium text-sm text-muted-foreground">
                              Thành phố trực thuộc trung ương
                            </h4>
                            <div className="grid grid-cols-5 gap-4">
                              {CENTRAL_CITIES.map(city => (
                                <button
                                  key={city.code}
                                  onClick={() => {
                                    handleLocationChange('province', city.code);
                                    setSelectedDistrict('');
                                    setSelectedWard('');
                                  }}
                                  className={`relative group rounded-lg overflow-hidden border-2 transition-all ${
                                    selectedProvince === city.code
                                      ? 'border-primary shadow-lg scale-105'
                                      : 'border-transparent hover:border-primary/50'
                                  }`}
                                >
                                  <div className="aspect-[4/3] relative">
                                    <Image
                                      src={city.image}
                                      alt={city.name}
                                      fill
                                      className="object-cover"
                                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                    />
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                                    <div className="absolute bottom-0 left-0 right-0 p-3 text-white">
                                      <div className="flex items-center gap-2">
                                        <span className="text-xl">{city.icon}</span>
                                        <span className="font-medium">{city.name}</span>
                                      </div>
                                    </div>
                                  </div>
                                </button>
                              ))}
                            </div>
                          </div>

                          {/* Other Cities Grid */}
                          <div className="space-y-4">
                            <h4 className="font-medium text-sm text-muted-foreground">
                              Tỉnh/Thành phố khác
                            </h4>
                            <div className="grid grid-cols-5 gap-2">
                              {provinces
                                .filter(
                                  province =>
                                    !CENTRAL_CITIES.find(
                                      city => city.code === province.code.toString()
                                    )
                                )
                                .map(province => (
                                  <button
                                    key={province.code}
                                    onClick={() => {
                                      handleLocationChange('province', province.code.toString());
                                      setSelectedDistrict('');
                                      setSelectedWard('');
                                    }}
                                    className={`w-full p-2 rounded-md hover:bg-muted flex items-center gap-2 ${
                                      selectedProvince === province.code.toString()
                                        ? 'bg-primary text-primary-foreground'
                                        : ''
                                    }`}
                                  >
                                    <span className="text-xl">🏘️</span>
                                    <span className="text-sm">{province.name}</span>
                                  </button>
                                ))}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* District Selection */}
                      {selectedProvince && !selectedDistrict && (
                        <div className="space-y-4">
                          <h4 className="font-medium text-sm text-muted-foreground">Quận/Huyện</h4>
                          <div className="grid grid-cols-5 gap-2">
                            {districts.map(district => (
                              <button
                                key={district.code}
                                onClick={() => {
                                  handleLocationChange('district', district.code.toString());
                                  setSelectedWard('');
                                }}
                                className={`w-full p-2 rounded-md hover:bg-muted flex items-center gap-2 ${
                                  selectedDistrict === district.code.toString()
                                    ? 'bg-primary text-primary-foreground'
                                    : ''
                                }`}
                              >
                                <span className="text-xl">🏢</span>
                                <span className="text-sm">{district.name}</span>
                              </button>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Ward Selection */}
                      {selectedDistrict && !selectedWard && (
                        <div className="space-y-4">
                          <h4 className="font-medium text-sm text-muted-foreground">Phường/Xã</h4>
                          <div className="grid grid-cols-5 gap-2">
                            {wards.map(ward => (
                              <button
                                key={ward.code}
                                onClick={() => handleLocationChange('ward', ward.code.toString())}
                                className={`w-full p-2 rounded-md hover:bg-muted flex items-center gap-2 ${
                                  selectedWard === ward.code.toString()
                                    ? 'bg-primary text-primary-foreground'
                                    : ''
                                }`}
                              >
                                <span className="text-xl">🏠</span>
                                <span className="text-sm">{ward.name}</span>
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Property Type Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={`max-sm:hidden w-full focus-visible:ring-0 focus-visible:outline-none ${activeFilters.propertyType ? 'bg-red-50 border-red-200 hover:bg-red-100' : 'text-muted-foreground'}`}
                  >
                    <div className="flex items-center">
                      <Building
                        className={`${activeFilters.propertyType ? 'text-red-600' : 'text-muted-foreground'} mr-2 h-4 w-4`}
                      />
                      <span
                        className={`truncate ${activeFilters.propertyType ? 'text-red-600' : 'text-muted-foreground'}`}
                      >
                        {activeFilters.propertyType
                          ? activeFilters.propertyType.includes(',')
                            ? `${activeFilters.propertyType.split(',').length} loại`
                            : {
                                apartment: 'Chung cư',
                                villa: 'Biệt thự',
                                land_plot: 'Đất',
                                shop_house: 'Nhà phố',
                              }[activeFilters.propertyType]
                          : 'Loại nhà'}
                      </span>
                    </div>
                    <ChevronDown
                      className={`${activeFilters.propertyType ? 'text-red-600' : 'text-muted-foreground'} h-4 w-4 ml-2`}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[calc(100vw-2rem)] sm:w-[280px] font-mann">
                  <DropdownMenuLabel className="text-sm font-normal">Loại nhà</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <DropdownMenuCheckboxItem
                      checked={selectedPropertyTypes.length === propertyTypes.length}
                      onCheckedChange={handleSelectAllPropertyTypes}
                      onSelect={e => e.preventDefault()}
                      className="flex items-center py-2"
                    >
                      <div className="flex items-center space-x-2 w-full">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <span>Chọn tất cả</span>
                      </div>
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuSeparator />
                    {propertyTypes.map(type => (
                      <DropdownMenuCheckboxItem
                        key={type.id}
                        checked={selectedPropertyTypes.includes(type.id)}
                        onCheckedChange={() => handlePropertyTypeChange(type.id)}
                        onSelect={e => e.preventDefault()}
                        className="flex items-center py-2"
                      >
                        <div className="flex items-center space-x-2 w-full">
                          <type.icon className="h-4 w-4 text-muted-foreground" />
                          <span>{type.label}</span>
                        </div>
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <Button
                      className="w-full bg-red-600 hover:bg-red-700 text-white border-red-600 focus-visible:ring-0 focus-visible:outline-none"
                      onClick={handleApplyPropertyTypes}
                    >
                      Áp dụng
                    </Button>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Transaction Type Dropdown */}
              <DropdownMenu open={isTransactionTypeOpen} onOpenChange={setIsTransactionTypeOpen}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={`max-xl:hidden w-full focus-visible:ring-0 focus-visible:outline-none ${activeFilters.transactionType ? 'bg-red-50 border-red-200 hover:bg-red-100' : 'text-muted-foreground'}`}
                  >
                    <div className="flex items-center">
                      <Home
                        className={`${activeFilters.transactionType ? 'text-red-600' : 'text-muted-foreground'} mr-2 h-4 w-4`}
                      />
                      <span
                        className={`truncate ${activeFilters.transactionType ? 'text-red-600' : 'text-muted-foreground'}`}
                      >
                        {activeFilters.transactionType
                          ? activeFilters.transactionType === TransactionType.FOR_SALE
                            ? 'Bán'
                            : 'Thuê'
                          : 'Mua/Thuê'}
                      </span>
                    </div>
                    <ChevronDown
                      className={`${activeFilters.transactionType ? 'text-red-600' : 'text-muted-foreground'} h-4 w-4 ml-2`}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[calc(100vw-2rem)] sm:w-[280px] font-mann">
                  <DropdownMenuLabel className="text-sm font-normal">Mua/Thuê</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <DropdownMenuRadioGroup
                      value={selectedTransactionType}
                      onValueChange={handleTransactionTypeChange}
                    >
                      {transactionTypes.map(type => (
                        <DropdownMenuRadioItem
                          key={type.id}
                          value={type.id}
                          onSelect={e => e.preventDefault()}
                          className="flex items-center py-2"
                        >
                          <div className="flex items-center space-x-2 w-full">
                            <type.icon className="h-4 w-4 text-muted-foreground" />
                            <span>{type.label}</span>
                          </div>
                        </DropdownMenuRadioItem>
                      ))}
                    </DropdownMenuRadioGroup>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <Button
                      className="w-full bg-red-600 hover:bg-red-700 text-white border-red-600 focus-visible:ring-0 focus-visible:outline-none"
                      onClick={handleApplyTransactionType}
                    >
                      Áp dụng
                    </Button>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Price Range Dropdown */}
              <DropdownMenu open={isPriceRangeOpen} onOpenChange={setIsPriceRangeOpen}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={`max-md:hidden w-full focus-visible:ring-0 focus-visible:outline-none ${activeFilters.priceRange ? 'bg-red-50 border-red-200 hover:bg-red-100' : 'text-muted-foreground'}`}
                  >
                    <div className="flex items-center">
                      <DollarSign
                        className={`${activeFilters.priceRange ? 'text-red-600' : 'text-muted-foreground'} mr-2 h-4 w-4`}
                      />
                      <span
                        className={`truncate ${activeFilters.priceRange ? 'text-red-600' : 'text-muted-foreground'}`}
                      >
                        {formatPriceDisplay(activeFilters.priceRange || '')}
                      </span>
                    </div>
                    <ChevronDown
                      className={`${activeFilters.priceRange ? 'text-red-600' : 'text-muted-foreground'} h-4 w-4 ml-2`}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[calc(100vw-2rem)] sm:w-[400px] p-4">
                  <DropdownMenuLabel className="text-sm font-normal">
                    {activeFilters.transactionType === TransactionType.FOR_RENT
                      ? 'Giá thuê'
                      : 'Giá bán'}
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <div className="space-y-4 font-mann py-2">
                      {/* Price Inputs */}
                      <div className="flex justify-between items-center gap-2">
                        <div className="flex flex-col w-1/2">
                          <label className="text-sm text-muted-foreground mb-1">
                            {activeFilters.transactionType === TransactionType.FOR_RENT
                              ? 'Giá thuê tối thiểu'
                              : 'Giá thấp nhất'}
                          </label>
                          <div className="relative">
                            <Input
                              value={minPrice}
                              onChange={e => {
                                const rawValue = e.target.value.replace(/[^0-9]/g, '');
                                setMinPrice(rawValue);
                                setSliderValue([Number(rawValue), sliderValue[1]]);
                              }}
                              onBlur={() => {
                                if (minPrice) setMinPrice(formatCurrency(Number(minPrice), 'VND'));
                              }}
                              onFocus={() => {
                                setMinPrice(sliderValue[0]?.toString() || '');
                              }}
                              type="text"
                              placeholder="Từ"
                              inputMode="numeric"
                              className="pr-8"
                            />
                            <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                              {activeFilters.transactionType === TransactionType.FOR_RENT
                                ? 'VND/tháng'
                                : 'VND'}
                            </span>
                          </div>
                        </div>
                        <div className="flex flex-col w-1/2">
                          <label className="text-sm text-muted-foreground mb-1">
                            {activeFilters.transactionType === TransactionType.FOR_RENT
                              ? 'Giá thuê tối đa'
                              : 'Giá cao nhất'}
                          </label>
                          <div className="relative">
                            <Input
                              value={maxPrice}
                              onChange={e => {
                                const rawValue = e.target.value.replace(/[^0-9]/g, '');
                                setMaxPrice(rawValue);
                                setSliderValue([sliderValue[0], Number(rawValue)]);
                              }}
                              onBlur={() => {
                                if (maxPrice) {
                                  const formatted = formatCurrency(Number(maxPrice), 'VND');
                                  setMaxPrice(formatted);
                                }
                              }}
                              onFocus={() => {
                                setMaxPrice(sliderValue[1]?.toString() || '');
                              }}
                              type="text"
                              placeholder="Đến"
                              inputMode="numeric"
                              className="pr-8"
                            />
                            <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                              {activeFilters.transactionType === TransactionType.FOR_RENT
                                ? 'VND/tháng'
                                : 'VND'}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Price Distribution Chart */}
                      <div className="h-[100px] w-full">
                        <ResponsiveContainer width="100%" height="100%">
                          <AreaChart
                            data={getCurrentPriceRanges().distribution}
                            margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                          >
                            <defs>
                              <linearGradient id="colorCount" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor="#ef4444" stopOpacity={0.1} />
                                <stop offset="95%" stopColor="#ef4444" stopOpacity={0} />
                              </linearGradient>
                            </defs>
                            <XAxis
                              dataKey="price"
                              tickFormatter={value => {
                                const isRent =
                                  activeFilters.transactionType === TransactionType.FOR_RENT;
                                return `${value}${isRent ? 'tr' : 'tỷ'}`;
                              }}
                              tick={{ fontSize: 10 }}
                              axisLine={false}
                              tickLine={false}
                            />
                            <YAxis hide={true} domain={[0, 'dataMax']} />
                            <Tooltip
                              content={({ active, payload }) => {
                                if (active && payload && payload.length) {
                                  const isRent =
                                    activeFilters.transactionType === TransactionType.FOR_RENT;
                                  const isLastItem =
                                    payload[0].payload.price === (isRent ? '70+' : '9-10');
                                  const unit = isRent ? 'tr' : 'tỷ';
                                  return (
                                    <div className="bg-white p-2 border rounded shadow-sm">
                                      <p className="text-sm font-medium">
                                        {payload[0].payload.price}
                                        {unit}
                                        {isLastItem ? '+' : ''}: {payload[0].value} bất động sản
                                      </p>
                                    </div>
                                  );
                                }
                                return null;
                              }}
                            />
                            <Area
                              type="monotone"
                              dataKey="count"
                              stroke="#ef4444"
                              strokeWidth={2}
                              fill="url(#colorCount)"
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </div>

                      {/* Price Slider */}
                      <div className="px-2">
                        <Slider
                          min={0}
                          max={getCurrentPriceRanges().maxValue}
                          step={getCurrentPriceRanges().step}
                          value={sliderValue}
                          onValueChange={setSliderValue}
                          className="relative flex w-full touch-none select-none items-center"
                        />
                        <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                          <span>{formatPriceLabel(sliderValue[0])}</span>
                          <span>
                            {formatPriceLabel(
                              sliderValue[1],
                              sliderValue[1] === getCurrentPriceRanges().maxValue
                            )}
                          </span>
                        </div>
                      </div>

                      {/* Quick Price Ranges */}
                      <div className="grid grid-cols-2 gap-2">
                        {getCurrentPriceRanges().quickRanges.map(range => (
                          <Button
                            key={range.label}
                            variant="outline"
                            className="text-sm h-8"
                            onClick={() => {
                              setSliderValue(range.value);
                              setMinPrice(range.value[0].toString());
                              setMaxPrice(range.value[1].toString());
                            }}
                          >
                            {range.label}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <div className="flex justify-between">
                      <Button
                        className="text-red-600 hover:text-red-700"
                        variant="ghost"
                        onClick={handleReset}
                      >
                        Đặt lại
                      </Button>
                      <Button
                        className="bg-red-600 hover:bg-red-700 text-white border-red-600 focus-visible:ring-0 focus-visible:outline-none"
                        onClick={() => {
                          handleApply();
                          setIsPriceRangeOpen(false);
                        }}
                      >
                        Áp dụng
                      </Button>
                    </div>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Property Size Dropdown */}
              <DropdownMenu open={isPropertySizeOpen} onOpenChange={setIsPropertySizeOpen}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={`max-xl:hidden w-full focus-visible:ring-0 focus-visible:outline-none ${activeFilters.propertySize ? 'bg-red-50 border-red-200 hover:bg-red-100' : 'text-muted-foreground'}`}
                  >
                    <div className="flex items-center">
                      <Ruler
                        className={`${activeFilters.propertySize ? 'text-red-600' : 'text-muted-foreground'} mr-2 h-4 w-4`}
                      />
                      <span
                        className={`truncate ${activeFilters.propertySize ? 'text-red-600' : 'text-muted-foreground'}`}
                      >
                        {formatSizeDisplay(activeFilters.propertySize || '')}
                      </span>
                    </div>
                    <ChevronDown
                      className={`${activeFilters.propertySize ? 'text-red-600' : 'text-muted-foreground'} h-4 w-4 ml-2`}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[calc(100vw-2rem)] sm:w-[400px] p-4">
                  <DropdownMenuLabel className="text-sm font-normal">Diện tích</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <div className="space-y-4 font-mann pb-2">
                      {/* Size Inputs */}
                      <div className="flex justify-between items-center gap-2">
                        <div className="flex flex-col w-1/2">
                          <label className="text-sm text-muted-foreground mb-1">
                            Diện tích tối thiểu
                          </label>
                          <div className="relative">
                            <Input
                              value={minSize}
                              onChange={e => {
                                const rawValue = e.target.value.replace(/[^0-9]/g, '');
                                setMinSize(rawValue);
                                setSizeSliderValue([Number(rawValue), sizeSliderValue[1]]);
                              }}
                              onBlur={() => {
                                if (minSize) setMinSize(formatSizeLabel(Number(minSize)));
                              }}
                              onFocus={() => {
                                setMinSize(sizeSliderValue[0]?.toString() || '');
                              }}
                              type="text"
                              placeholder="Từ"
                              inputMode="numeric"
                              className="pr-8"
                            />
                            <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                              m²
                            </span>
                          </div>
                        </div>
                        <div className="flex flex-col w-1/2">
                          <label className="text-sm text-muted-foreground mb-1">
                            Diện tích tối đa
                          </label>
                          <div className="relative">
                            <Input
                              value={maxSize}
                              onChange={e => {
                                const rawValue = e.target.value.replace(/[^0-9]/g, '');
                                setMaxSize(rawValue);
                                setSizeSliderValue([sizeSliderValue[0], Number(rawValue)]);
                              }}
                              onBlur={() => {
                                if (maxSize) setMaxSize(formatSizeLabel(Number(maxSize)));
                              }}
                              onFocus={() => {
                                setMaxSize(sizeSliderValue[1]?.toString() || '');
                              }}
                              type="text"
                              placeholder="Đến"
                              inputMode="numeric"
                              className="pr-8"
                            />
                            <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                              m²
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Size Distribution Chart */}
                      <div className="h-[100px] w-full">
                        <ResponsiveContainer width="100%" height="100%">
                          <AreaChart
                            data={sizeDistribution}
                            margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                          >
                            <defs>
                              <linearGradient id="colorSizeCount" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor="#ef4444" stopOpacity={0.1} />
                                <stop offset="95%" stopColor="#ef4444" stopOpacity={0} />
                              </linearGradient>
                            </defs>
                            <XAxis
                              dataKey="size"
                              tickFormatter={value => `${value}m²`}
                              tick={{ fontSize: 10 }}
                              axisLine={false}
                              tickLine={false}
                            />
                            <YAxis hide={true} domain={[0, 'dataMax']} />
                            <Tooltip
                              content={({ active, payload }) => {
                                if (active && payload && payload.length) {
                                  const isLastItem = payload[0].payload.size === '1000+';
                                  return (
                                    <div className="bg-white p-2 border rounded shadow-sm">
                                      <p className="text-sm font-medium">
                                        {payload[0].payload.size}
                                        {isLastItem ? '+' : ''}: {payload[0].value} bất động sản
                                      </p>
                                    </div>
                                  );
                                }
                                return null;
                              }}
                            />
                            <Area
                              type="monotone"
                              dataKey="count"
                              stroke="#ef4444"
                              strokeWidth={2}
                              fill="url(#colorSizeCount)"
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </div>

                      {/* Size Slider */}
                      <div className="px-2">
                        <Slider
                          min={0}
                          max={1000}
                          step={5}
                          value={sizeSliderValue}
                          onValueChange={setSizeSliderValue}
                          className="relative flex w-full touch-none select-none items-center"
                        />
                        <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                          <span>{formatSizeLabel(sizeSliderValue[0])}</span>
                          <span>
                            {formatSizeLabel(sizeSliderValue[1], sizeSliderValue[1] === 1000)}
                          </span>
                        </div>
                      </div>

                      {/* Quick Size Ranges */}
                      <div className="grid grid-cols-2 gap-2">
                        {quickSizeRanges.map(range => (
                          <Button
                            key={range.label}
                            variant="outline"
                            className="text-sm h-8"
                            onClick={() => {
                              setSizeSliderValue(range.value);
                              setMinSize(range.value[0].toString());
                              setMaxSize(range.value[1].toString());
                            }}
                          >
                            {range.label}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <div className="flex justify-between">
                      <Button
                        className="text-red-600 hover:text-red-700"
                        variant="ghost"
                        onClick={handleResetSize}
                      >
                        Đặt lại
                      </Button>
                      <Button
                        className="bg-red-600 hover:bg-red-700 text-white border-red-600 focus-visible:ring-0 focus-visible:outline-none"
                        onClick={handleApplySize}
                      >
                        Áp dụng
                      </Button>
                    </div>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Bed Count Dropdown */}
              <DropdownMenu open={isRoomsDropdownOpen} onOpenChange={setIsRoomsDropdownOpen}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={`max-md:hidden w-full focus-visible:ring-0 focus-visible:outline-none ${
                      activeFilters.bedCountDisplay || activeFilters.bathCountDisplay
                        ? 'bg-red-50 border-red-200 hover:bg-red-100'
                        : 'text-muted-foreground'
                    }`}
                  >
                    <div className="flex items-center">
                      <BedDouble
                        className={`${
                          activeFilters.bedCountDisplay || activeFilters.bathCountDisplay
                            ? 'text-red-600'
                            : 'text-muted-foreground'
                        } mr-2 h-4 w-4`}
                      />
                      <span
                        className={`truncate ${
                          activeFilters.bedCountDisplay || activeFilters.bathCountDisplay
                            ? 'text-red-600'
                            : 'text-muted-foreground'
                        }`}
                      >
                        {activeFilters.bedCountDisplay && activeFilters.bathCountDisplay
                          ? `${activeFilters.bedCountDisplay}, ${activeFilters.bathCountDisplay}`
                          : activeFilters.bedCountDisplay ||
                            activeFilters.bathCountDisplay ||
                            'Số phòng'}
                      </span>
                    </div>
                    <ChevronDown
                      className={`${
                        activeFilters.bedCountDisplay || activeFilters.bathCountDisplay
                          ? 'text-red-600'
                          : 'text-muted-foreground'
                      } h-4 w-4 ml-2`}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[calc(100vw-2rem)] sm:w-[400px]">
                  <DropdownMenuLabel className="text-sm font-normal">Số phòng</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <div className="p-4 space-y-6 font-mann">
                      {/* Bed Count Section */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-sm">Số phòng ngủ</h4>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="exact-bed-match"
                              checked={isExactBedMatch}
                              onCheckedChange={(checked: boolean) => setIsExactBedMatch(checked)}
                            />
                            <label
                              htmlFor="exact-bed-match"
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              Chính xác số phòng
                            </label>
                          </div>
                        </div>
                        <RadioGroup.Root
                          value={selectedBedCount}
                          onValueChange={handleBedCountChange}
                          className="max-w-sm w-full grid grid-cols-3 gap-3"
                        >
                          {(isExactBedMatch ? exactBedOptions : bedOptions).map(option => (
                            <RadioGroup.Item
                              key={option.id}
                              value={option.id}
                              className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                            >
                              <span className="font-medium text-sm">{option.label}</span>
                            </RadioGroup.Item>
                          ))}
                        </RadioGroup.Root>
                      </div>

                      <DropdownMenuSeparator />

                      {/* Bath Count Section */}
                      <div className="space-y-4">
                        <h4 className="font-medium text-sm">Số phòng tắm</h4>
                        <RadioGroup.Root
                          value={selectedBathCount}
                          onValueChange={handleBathCountChange}
                          className="max-w-sm w-full grid grid-cols-3 gap-3"
                        >
                          {bathOptions.map(option => (
                            <RadioGroup.Item
                              key={option.id}
                              value={option.id}
                              className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                            >
                              <span className="font-medium text-sm">{option.label}</span>
                            </RadioGroup.Item>
                          ))}
                        </RadioGroup.Root>
                      </div>
                    </div>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <Button
                      className="w-full bg-red-600 hover:bg-red-700 text-white border-red-600 focus-visible:ring-0 focus-visible:outline-none"
                      onClick={handleApplyRooms}
                    >
                      Áp dụng
                    </Button>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Action Buttons - Full width on mobile */}
              <div className="col-span-2 sm:col-span-3 md:col-span-4 flex flex-row gap-2">
                {/* Search Button */}
                <Button
                  className="w-full lg:w-auto bg-red-600 hover:bg-red-700 focus-visible:ring-0 focus-visible:outline-none"
                  onClick={handleSearch}
                  disabled={isSearching}
                >
                  {isSearching ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <SearchIcon className="h-4 w-4" />
                  )}
                </Button>

                {/* More Filters Button */}
                <Sheet open={isMoreFiltersOpen} onOpenChange={setIsMoreFiltersOpen}>
                  <SheetTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full lg:w-auto text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <SlidersHorizontal className="h-4 w-4" />
                    </Button>
                  </SheetTrigger>
                  <SheetContent className="w-full sm:max-w-xl font-mann">
                    <SheetHeader>
                      <SheetTitle>Bộ lọc tìm kiếm</SheetTitle>
                    </SheetHeader>
                    <ScrollArea className="h-full">
                      <div className="flex flex-col gap-2.5 p-4 pt-0 pb-24 space-y-6">
                        {/* Property Type Section */}
                        <div className="space-y-4">
                          <h3 className=" text-base">Loại bất động sản</h3>
                          <div className="w-full grid grid-cols-2 md:grid-cols-4 gap-3">
                            {propertyTypes.map(type => (
                              <CheckboxPrimitive.Root
                                key={type.id}
                                onClick={() => handlePropertyTypeChange(type.id)}
                                className="relative ring-[1px] ring-border rounded-lg px-4 py-3 text-start text-foreground data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:text-red-600"
                              >
                                <type.icon className="mb-3 size-4" />
                                <span className="font-medium text-sm tracking-tight">
                                  {type.label}
                                </span>
                                <CheckboxPrimitive.Indicator className="absolute top-2 right-2">
                                  <CircleCheck className="fill-red-600 text-white" />
                                </CheckboxPrimitive.Indicator>
                              </CheckboxPrimitive.Root>
                            ))}
                          </div>
                        </div>
                        <Separator />

                        {/* Transaction Type Section */}
                        <div className="space-y-4">
                          <h3 className=" text-base">Loại giao dịch</h3>
                          <RadioGroup.Root
                            value={selectedTransactionType}
                            onValueChange={handleTransactionTypeChange}
                            className="grid grid-cols-3 gap-2"
                          >
                            {transactionTypes.map(type => (
                              <RadioGroup.Item
                                key={type.id}
                                value={type.id}
                                className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                              >
                                <div className="flex flex-col items-center gap-2">
                                  <type.icon className="h-5 w-5" />
                                  <span className="font-medium text-sm">{type.label}</span>
                                </div>
                              </RadioGroup.Item>
                            ))}
                          </RadioGroup.Root>
                        </div>

                        <Separator />

                        {/* Price Range Section */}
                        <div className="space-y-4">
                          <h3 className="font-medium text-lg">
                            {activeFilters.transactionType === TransactionType.FOR_RENT
                              ? 'Giá thuê'
                              : 'Giá bán'}
                          </h3>
                          <div className="space-y-4">
                            {/* Price Distribution Chart */}
                            <div className="h-[100px] w-full">
                              <ResponsiveContainer width="100%" height="100%">
                                <AreaChart
                                  data={getCurrentPriceRanges().distribution}
                                  margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                                >
                                  <defs>
                                    <linearGradient id="colorCount" x1="0" y1="0" x2="0" y2="1">
                                      <stop offset="5%" stopColor="#ef4444" stopOpacity={0.1} />
                                      <stop offset="95%" stopColor="#ef4444" stopOpacity={0} />
                                    </linearGradient>
                                  </defs>
                                  <XAxis
                                    dataKey="price"
                                    tickFormatter={value => {
                                      const isRent =
                                        activeFilters.transactionType === TransactionType.FOR_RENT;
                                      return `${value}${isRent ? 'tr' : 'tỷ'}`;
                                    }}
                                    tick={{ fontSize: 10 }}
                                    axisLine={false}
                                    tickLine={false}
                                  />
                                  <YAxis hide={true} domain={[0, 'dataMax']} />
                                  <Tooltip
                                    content={({ active, payload }) => {
                                      if (active && payload && payload.length) {
                                        const isRent =
                                          activeFilters.transactionType ===
                                          TransactionType.FOR_RENT;
                                        const isLastItem =
                                          payload[0].payload.price === (isRent ? '70+' : '9-10');
                                        const unit = isRent ? 'tr' : 'tỷ';
                                        return (
                                          <div className="bg-white p-2 border rounded shadow-sm">
                                            <p className="text-sm font-medium">
                                              {payload[0].payload.price}
                                              {unit}
                                              {isLastItem ? '+' : ''}: {payload[0].value} bất động
                                              sản
                                            </p>
                                          </div>
                                        );
                                      }
                                      return null;
                                    }}
                                  />
                                  <Area
                                    type="monotone"
                                    dataKey="count"
                                    stroke="#ef4444"
                                    strokeWidth={2}
                                    fill="url(#colorCount)"
                                  />
                                </AreaChart>
                              </ResponsiveContainer>
                            </div>

                            {/* Price Inputs */}
                            <div className="flex justify-between items-center gap-2">
                              <div className="flex flex-col w-1/2">
                                <label className="text-sm text-muted-foreground mb-1">
                                  {activeFilters.transactionType === TransactionType.FOR_RENT
                                    ? 'Giá thuê tối thiểu'
                                    : 'Giá thấp nhất'}
                                </label>
                                <div className="relative">
                                  <Input
                                    value={minPrice}
                                    onChange={e => {
                                      const rawValue = e.target.value.replace(/[^0-9]/g, '');
                                      setMinPrice(rawValue);
                                      setSliderValue([Number(rawValue), sliderValue[1]]);
                                    }}
                                    onBlur={() => {
                                      if (minPrice)
                                        setMinPrice(formatCurrency(Number(minPrice), 'VND'));
                                    }}
                                    onFocus={() => {
                                      setMinPrice(sliderValue[0]?.toString() || '');
                                    }}
                                    type="text"
                                    placeholder="Từ"
                                    inputMode="numeric"
                                    className="pr-8"
                                  />
                                  <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                    {activeFilters.transactionType === TransactionType.FOR_RENT
                                      ? 'VND/tháng'
                                      : 'VND'}
                                  </span>
                                </div>
                              </div>
                              <div className="flex flex-col w-1/2">
                                <label className="text-sm text-muted-foreground mb-1">
                                  {activeFilters.transactionType === TransactionType.FOR_RENT
                                    ? 'Giá thuê tối đa'
                                    : 'Giá cao nhất'}
                                </label>
                                <div className="relative">
                                  <Input
                                    value={maxPrice}
                                    onChange={e => {
                                      const rawValue = e.target.value.replace(/[^0-9]/g, '');
                                      setMaxPrice(rawValue);
                                      setSliderValue([sliderValue[0], Number(rawValue)]);
                                    }}
                                    onBlur={() => {
                                      if (maxPrice)
                                        setMaxPrice(formatCurrency(Number(maxPrice), 'VND'));
                                    }}
                                    onFocus={() => {
                                      setMaxPrice(sliderValue[1]?.toString() || '');
                                    }}
                                    type="text"
                                    placeholder="Đến"
                                    inputMode="numeric"
                                    className="pr-8"
                                  />
                                  <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                    {activeFilters.transactionType === TransactionType.FOR_RENT
                                      ? 'VND/tháng'
                                      : 'VND'}
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Price Slider */}
                            <div className="px-2">
                              <Slider
                                min={0}
                                max={getCurrentPriceRanges().maxValue}
                                step={getCurrentPriceRanges().step}
                                value={sliderValue}
                                onValueChange={setSliderValue}
                                className="relative flex w-full touch-none select-none items-center"
                              />
                              <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                                <span>{formatPriceLabel(sliderValue[0])}</span>
                                <span>
                                  {formatPriceLabel(
                                    sliderValue[1],
                                    sliderValue[1] === getCurrentPriceRanges().maxValue
                                  )}
                                </span>
                              </div>
                            </div>

                            {/* Quick Price Ranges */}
                            <div className="grid grid-cols-2 gap-2">
                              {getCurrentPriceRanges().quickRanges.map(range => (
                                <Button
                                  key={range.label}
                                  variant="outline"
                                  className="text-sm h-8"
                                  onClick={() => {
                                    setSliderValue(range.value);
                                    setMinPrice(range.value[0].toString());
                                    setMaxPrice(range.value[1].toString());
                                  }}
                                >
                                  {range.label}
                                </Button>
                              ))}
                            </div>
                          </div>
                        </div>

                        <Separator />

                        {/* Property Size Section */}
                        <div className="space-y-4">
                          <h3 className="font-medium text-lg">Diện tích</h3>
                          <div className="space-y-4">
                            {/* Size Distribution Chart */}
                            <div className="h-[100px] w-full">
                              <ResponsiveContainer width="100%" height="100%">
                                <AreaChart
                                  data={sizeDistribution}
                                  margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                                >
                                  <defs>
                                    <linearGradient id="colorSizeCount" x1="0" y1="0" x2="0" y2="1">
                                      <stop offset="5%" stopColor="#ef4444" stopOpacity={0.1} />
                                      <stop offset="95%" stopColor="#ef4444" stopOpacity={0} />
                                    </linearGradient>
                                  </defs>
                                  <XAxis
                                    dataKey="size"
                                    tickFormatter={value => `${value}m²`}
                                    tick={{ fontSize: 10 }}
                                    axisLine={false}
                                    tickLine={false}
                                  />
                                  <YAxis hide={true} domain={[0, 'dataMax']} />
                                  <Tooltip
                                    content={({ active, payload }) => {
                                      if (active && payload && payload.length) {
                                        const isLastItem = payload[0].payload.size === '1000+';
                                        return (
                                          <div className="bg-white p-2 border rounded shadow-sm">
                                            <p className="text-sm font-medium">
                                              {payload[0].payload.size}
                                              {isLastItem ? '+' : ''}: {payload[0].value} bất động
                                              sản
                                            </p>
                                          </div>
                                        );
                                      }
                                      return null;
                                    }}
                                  />
                                  <Area
                                    type="monotone"
                                    dataKey="count"
                                    stroke="#ef4444"
                                    strokeWidth={2}
                                    fill="url(#colorSizeCount)"
                                  />
                                </AreaChart>
                              </ResponsiveContainer>
                            </div>

                            {/* Size Inputs */}
                            <div className="flex justify-between items-center gap-2">
                              <div className="flex flex-col w-1/2">
                                <label className="text-sm text-muted-foreground mb-1">
                                  Diện tích tối thiểu
                                </label>
                                <div className="relative">
                                  <Input
                                    value={minSize}
                                    onChange={e => {
                                      const rawValue = e.target.value.replace(/[^0-9]/g, '');
                                      setMinSize(rawValue);
                                      setSizeSliderValue([Number(rawValue), sizeSliderValue[1]]);
                                    }}
                                    onBlur={() => {
                                      if (minSize) setMinSize(formatSizeLabel(Number(minSize)));
                                    }}
                                    onFocus={() => {
                                      setMinSize(sizeSliderValue[0]?.toString() || '');
                                    }}
                                    type="text"
                                    placeholder="Từ"
                                    inputMode="numeric"
                                    className="pr-8"
                                  />
                                  <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                    m²
                                  </span>
                                </div>
                              </div>
                              <div className="flex flex-col w-1/2">
                                <label className="text-sm text-muted-foreground mb-1">
                                  Diện tích tối đa
                                </label>
                                <div className="relative">
                                  <Input
                                    value={maxSize}
                                    onChange={e => {
                                      const rawValue = e.target.value.replace(/[^0-9]/g, '');
                                      setMaxSize(rawValue);
                                      setSizeSliderValue([sizeSliderValue[0], Number(rawValue)]);
                                    }}
                                    onBlur={() => {
                                      if (maxSize) setMaxSize(formatSizeLabel(Number(maxSize)));
                                    }}
                                    onFocus={() => {
                                      setMaxSize(sizeSliderValue[1]?.toString() || '');
                                    }}
                                    type="text"
                                    placeholder="Đến"
                                    inputMode="numeric"
                                    className="pr-8"
                                  />
                                  <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                    m²
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Size Slider */}
                            <div className="px-2">
                              <Slider
                                min={0}
                                max={1000}
                                step={5}
                                value={sizeSliderValue}
                                onValueChange={setSizeSliderValue}
                                className="relative flex w-full touch-none select-none items-center"
                              />
                              <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                                <span>{formatSizeLabel(sizeSliderValue[0])}</span>
                                <span>
                                  {formatSizeLabel(sizeSliderValue[1], sizeSliderValue[1] === 1000)}
                                </span>
                              </div>
                            </div>

                            {/* Quick Size Ranges */}
                            <div className="grid grid-cols-2 gap-2">
                              {quickSizeRanges.map(range => (
                                <Button
                                  key={range.label}
                                  variant="outline"
                                  className="text-sm h-8"
                                  onClick={() => {
                                    setSizeSliderValue(range.value);
                                    setMinSize(range.value[0].toString());
                                    setMaxSize(range.value[1].toString());
                                  }}
                                >
                                  {range.label}
                                </Button>
                              ))}
                            </div>
                          </div>
                        </div>

                        <Separator />

                        {/* Bed Count Section */}
                        <div className="space-y-4">
                          <h3 className="font-medium text-lg">Số phòng ngủ</h3>
                          <div className="space-y-6">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium text-sm">Số phòng ngủ</h4>
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="exact-bed-match-sheet"
                                  checked={isExactBedMatch}
                                  onCheckedChange={(checked: boolean) =>
                                    setIsExactBedMatch(checked)
                                  }
                                />
                                <label
                                  htmlFor="exact-bed-match-sheet"
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                  Chính xác số phòng
                                </label>
                              </div>
                            </div>
                            <RadioGroup.Root
                              value={selectedBedCount}
                              onValueChange={handleBedCountChange}
                              className="grid grid-cols-3 gap-2"
                            >
                              {(isExactBedMatch ? exactBedOptions : bedOptions).map(option => (
                                <RadioGroup.Item
                                  key={option.id}
                                  value={option.id}
                                  className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                                >
                                  <span className="font-medium text-sm">{option.label}</span>
                                </RadioGroup.Item>
                              ))}
                            </RadioGroup.Root>
                          </div>
                        </div>

                        <Separator />

                        {/* Bath Count Section */}
                        <div className="space-y-4">
                          <h3 className="font-medium text-lg">Số phòng tắm</h3>
                          <RadioGroup.Root
                            value={selectedBathCount}
                            onValueChange={handleBathCountChange}
                            className="grid grid-cols-3 gap-2"
                          >
                            {bathOptions.map(option => (
                              <RadioGroup.Item
                                key={option.id}
                                value={option.id}
                                className="ring-1 ring-border rounded-md py-2 px-3 data-[state=checked]:ring-2 data-[state=checked]:ring-red-500 data-[state=checked]:bg-red-50 data-[state=checked]:text-red-600"
                              >
                                <span className="font-medium text-sm">{option.label}</span>
                              </RadioGroup.Item>
                            ))}
                          </RadioGroup.Root>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-between gap-2 pt-4">
                          <Button
                            variant="outline"
                            className="flex-1 text-red-600 hover:text-red-700"
                            onClick={() => {
                              handleReset();
                              handleResetSize();
                              setSelectedPropertyTypes([]);
                              setSelectedTransactionType('');
                              setSelectedBedCount('any');
                              setSelectedBathCount('any');
                              setIsExactBedMatch(false);
                            }}
                          >
                            Đặt lại
                          </Button>
                          <Button
                            className="flex-1 bg-red-600 hover:bg-red-700 text-white border-red-600 focus-visible:ring-0 focus-visible:outline-none"
                            onClick={() => {
                              handleApply();
                              handleApplySize();
                              handleApplyPropertyTypes();
                              handleApplyTransactionType();
                              handleApplyRooms();
                              setIsMoreFiltersOpen(false);
                            }}
                          >
                            Áp dụng
                          </Button>
                        </div>
                      </div>
                    </ScrollArea>
                  </SheetContent>
                </Sheet>

                {/* Clear Filters Button */}
                {countActiveFilters() > 0 && (
                  <Button
                    variant="outline"
                    onClick={clearFilters}
                    className="max-md:hidden w-full lg:w-auto text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <XIcon className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
