import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useUpdateProfile } from '@/hooks/useUsers';
import { toast } from 'sonner';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { AxiosError } from 'axios';

interface ProfileFormData {
  fullName: string;
  email: string;
  phoneNumber: string;
  about: string;
  userName: string;
  birthdate?: string;
  status?: string;
  avatarFile?: File;
}

interface EditProfileFormProps {
  profile: ProfileFormData;
  onSuccess: () => void;
  onCancel: () => void;
}

// Form validation schema
const profileFormSchema = z.object({
  fullName: z
    .string()
    .min(2, { message: 'Name must be at least 2 characters.' })
    .max(50, { message: 'Name must not be longer than 50 characters.' }),
  userName: z
    .string()
    .min(3, { message: 'Username must be at least 3 characters.' })
    .max(30, { message: 'Username must not be longer than 30 characters.' }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  phoneNumber: z
    .string()
    .min(5, { message: 'Phone number must be at least 5 characters.' })
    .max(15, { message: 'Phone number must not be longer than 15 characters.' })
    .optional()
    .or(z.literal('')),
  about: z
    .string()
    .max(1000, { message: 'Bio must not be longer than 500 characters.' })
    .optional()
    .or(z.literal('')),
  birthdate: z.date().optional(),
  status: z.enum(['Online', 'Idle', 'DoNotDisturb', 'Invisible']).optional(),
});

export function EditProfileForm({ profile, onSuccess, onCancel }: EditProfileFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { mutate: updateProfile } = useUpdateProfile();

  // Convert string date to Date object if it exists
  let birthdateValue: Date | undefined;
  if (profile.birthdate) {
    try {
      birthdateValue = new Date(profile.birthdate);
      // Check if date is valid
      if (isNaN(birthdateValue.getTime())) {
        birthdateValue = undefined;
      }
    } catch (error) {
      birthdateValue = undefined;
    }
  }

  // Create form
  const form = useForm<z.infer<typeof profileFormSchema>>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      fullName: profile.fullName || '',
      userName: profile.userName || '',
      email: profile.email || '',
      phoneNumber: profile.phoneNumber || '',
      about: profile.about || '',
      birthdate: birthdateValue,
      status: (profile.status as 'Online' | 'Idle' | 'DoNotDisturb' | 'Invisible') || 'Online',
    },
  });

  // Form submission handler
  const onSubmit = async (values: z.infer<typeof profileFormSchema>) => {
    setIsSubmitting(true);

    try {
      // Create FormData for submission
      const formData = new FormData();

      // Add all form fields to FormData
      formData.append('userName', values.userName);
      formData.append('fullName', values.fullName);
      formData.append('email', values.email);

      if (values.phoneNumber) {
        formData.append('phoneNumber', values.phoneNumber);
      }

      if (values.about) {
        formData.append('about', values.about);
      }

      if (values.birthdate) {
        // API expects birthDate field
        formData.append('birthDate', values.birthdate.toISOString());
      }

      if (values.status) {
        formData.append('status', values.status);
      }

      updateProfile(formData, {
        onSuccess: () => {
          toast.success('Thông tin đã được cập nhật thành công');
          onSuccess();
        },
        onError: (error: Error) => {
          console.error('Update profile error:', error);
          let errorMessage = 'Failed to update profile.';

          // Try to extract message from axios error if possible
          if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as unknown as AxiosError<{ message?: string }>;
            errorMessage = axiosError?.response?.data?.message || errorMessage;
          }

          toast.error(errorMessage);
        },
        onSettled: () => {
          setIsSubmitting(false);
        },
      });
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('Đã xảy ra lỗi khi gửi form.');
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full Name</FormLabel>
              <FormControl>
                <Input placeholder="User" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="userName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Username</FormLabel>
              <FormControl>
                <Input placeholder="user" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phoneNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone Number</FormLabel>
              <FormControl>
                <Input placeholder="+****************" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="birthdate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Birthdate</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={'outline'}
                      className={cn(
                        'w-full pl-3 text-left font-normal',
                        !field.value && 'text-muted-foreground'
                      )}
                    >
                      {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={date => date > new Date() || date < new Date('1900-01-01')}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="about"
          render={({ field }) => (
            <FormItem>
              <FormLabel>About</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Tell us a bit about yourself"
                  className="min-h-32"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex gap-3 justify-end pt-4">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving
              </>
            ) : (
              'Save Changes'
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
