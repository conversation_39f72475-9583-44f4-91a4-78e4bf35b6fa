'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import { Calendar as CalendarIcon, CheckCircle, MapPin, Clock, User } from 'lucide-react';
import { AppointmentResponse } from '@/lib/api/services/fetchAppointment';
import { useIsMobile } from '@/hooks/useMobile';
import Calendar20 from '@/components/calendar-20';
import { formatDate } from '@/utils/dates/formatDate';

interface AppointmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  propertyId: string;
  propertyName: string;
}

export default function AppointmentDialog({
  isOpen,
  onClose,
  propertyId,
  propertyName,
}: AppointmentDialogProps) {
  const [appointmentData, setAppointmentData] = useState<AppointmentResponse['data'] | null>(null);
  const isMobile = useIsMobile();

  const handleClose = () => {
    setAppointmentData(null);
    onClose();
  };

  // Success content component
  const AppointmentSuccessContent = () => {
    if (!appointmentData) return null;

    return (
      <div className="space-y-6">
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-green-600">Đặt lịch thành công!</h3>
            <p className="text-sm text-muted-foreground">Lịch hẹn xem nhà đã được tạo thành công</p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <CalendarIcon className="h-4 w-4" />
              Chi tiết lịch hẹn
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-3">
              <div className="flex items-center gap-3">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Thời gian</p>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(appointmentData.date)}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Địa điểm</p>
                  <p className="text-sm text-muted-foreground">
                    {appointmentData.location || propertyName}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <User className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Người hướng dẫn</p>
                  <p className="text-sm text-muted-foreground">
                    {appointmentData.saler?.fullName || 'Chưa xác định'}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="h-4 w-4" />
                <div>
                  <p className="text-sm font-medium">Trạng thái</p>
                  <Badge variant="secondary" className="text-xs">
                    {appointmentData.status}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex gap-2">
          <Button onClick={handleClose} className="flex-1">
            Đóng
          </Button>
          <Button
            variant="outline"
            onClick={() => window.open('/appointments', '_blank')}
            className="flex-1"
          >
            Xem lịch hẹn
          </Button>
        </div>
      </div>
    );
  };

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={handleClose}>
        <DrawerContent className="h-[90vh] font-mann">
          <DrawerHeader className="text-left">
            <DrawerTitle className="flex items-center gap-2 text-base">
              <CalendarIcon className="h-5 w-5" />
              {appointmentData ? 'Lịch hẹn đã được tạo' : `Đặt lịch hẹn`}
            </DrawerTitle>
            <DrawerDescription className="text-xs">
              {appointmentData
                ? 'Thông tin chi tiết về lịch hẹn của bạn'
                : 'Chọn ngày và giờ phù hợp để xem bất động sản'}
            </DrawerDescription>
          </DrawerHeader>
          <div className="flex-1 overflow-y-auto px-4">
            {appointmentData ? (
              <AppointmentSuccessContent />
            ) : (
              <Calendar20
                propertyId={propertyId}
                propertyName={propertyName}
                onSuccess={data => setAppointmentData(data)}
                onClose={handleClose}
                isDrawer={true}
              />
            )}
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[650px] font-mann">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            {appointmentData ? 'Lịch hẹn đã được tạo' : `Đặt lịch hẹn`}
          </DialogTitle>
          <DialogDescription>
            {appointmentData
              ? 'Thông tin chi tiết về lịch hẹn của bạn'
              : 'Chọn ngày và giờ phù hợp để xem bất động sản'}
          </DialogDescription>
        </DialogHeader>
        {appointmentData ? (
          <AppointmentSuccessContent />
        ) : (
          <Calendar20
            propertyId={propertyId}
            propertyName={propertyName}
            onSuccess={data => setAppointmentData(data)}
            onClose={handleClose}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
