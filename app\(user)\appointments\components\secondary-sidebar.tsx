// components/secondary-sidebar.tsx
import * as React from 'react';
import { Plus } from 'lucide-react';
import { DatePicker } from './date-picker';
import { Calendars } from './calendars';

const data = {
  user: {
    name: 'shadcn',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },
  calendars: [
    {
      name: 'My Calendars',
      items: ['Personal', 'Work', 'Family'],
    },
    {
      name: 'Favorites',
      items: ['Holidays', 'Birthdays'],
    },
    {
      name: 'Other',
      items: ['Travel', 'Reminders', 'Deadlines'],
    },
  ],
};

export function SecondarySidebar() {
  return (
    <div className="flex flex-col h-full">
      {/* Content */}
      <div className="flex-1 p-4 space-y-4">
        <DatePicker />
        <div className="border-t pt-4">
          <Calendars calendars={data.calendars} />
        </div>
      </div>

      {/* Footer */}
      <div className="border-t p-4">
        <button className="flex items-center gap-2 w-full p-2 hover:bg-muted rounded-md">
          <Plus className="h-4 w-4" />
          <span>New Calendar</span>
        </button>
      </div>
    </div>
  );
}
