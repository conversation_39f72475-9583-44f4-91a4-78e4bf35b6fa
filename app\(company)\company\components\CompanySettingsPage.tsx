import React, { useState } from 'react';
import {
  ArrowLeft,
  Building2,
  Upload,
  Save,
  Eye,
  EyeOff,
  Mail,
  Phone,
  MapPin,
  Globe,
  FileText,
  Hash,
  Camera,
  Trash2,
  Edit,
  Shield,
  Key,
  Bell,
  Palette,
  Users,
  CreditCard,
  Download,
  RefreshCw,
  Settings,
} from 'lucide-react';

interface CompanySettingsPageProps {
  onBack: () => void;
  businessData: any;
}

interface CompanyData {
  companyName: string;
  email: string;
  phone: string;
  address: string;
  website: string;
  businessLicense: string;
  taxCode: string;
  logo: File | null;
  description: string;
  businessType: string;
  foundedYear: string;
  employeeCount: string;
}

const CompanySettingsPage: React.FC<CompanySettingsPageProps> = ({ onBack, businessData }) => {
  const [activeTab, setActiveTab] = useState('general');
  const [isEditing, setIsEditing] = useState(false);
  const [showSensitiveInfo, setShowSensitiveInfo] = useState(false);
  const [companyData, setCompanyData] = useState<CompanyData>({
    companyName: businessData?.companyName || 'Công ty TNHH Revoland',
    email: businessData?.email || '<EMAIL>',
    phone: '0123 456 789',
    address: businessData?.address || '123 Đường ABC, Quận 1, TP.HCM',
    website: businessData?.website || 'https://revoland.com',
    businessLicense: businessData?.businessLicense || '**********',
    taxCode: businessData?.taxCode || '**********-001',
    logo: businessData?.logo || null,
    description:
      'Công ty chuyên về bất động sản với hơn 10 năm kinh nghiệm trong lĩnh vực mua bán, cho thuê và tư vấn bất động sản.',
    businessType: businessData?.businessType || 'buy-sell',
    foundedYear: '2014',
    employeeCount: '50-100',
  });

  const tabs = [
    { id: 'general', label: 'Thông tin chung', icon: Building2 },
    { id: 'security', label: 'Bảo mật', icon: Shield },
    { id: 'notifications', label: 'Thông báo', icon: Bell },
    { id: 'appearance', label: 'Giao diện', icon: Palette },
    { id: 'team', label: 'Nhóm', icon: Users },
    { id: 'billing', label: 'Thanh toán', icon: CreditCard },
  ];

  const handleInputChange = (field: keyof CompanyData, value: string) => {
    setCompanyData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setCompanyData(prev => ({ ...prev, logo: file }));
  };

  const handleSave = () => {
    setIsEditing(false);
    // Save logic here
    console.log('Saving company data:', companyData);
  };

  const renderGeneralTab = () => (
    <div className="space-y-8">
      {/* Company Logo */}
      <div className="bg-white rounded-2xl border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-bold text-gray-900">Logo Công ty</h3>
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-all"
          >
            <Edit size={16} />
            <span>{isEditing ? 'Hủy' : 'Chỉnh sửa'}</span>
          </button>
        </div>
        <div className="flex items-center gap-8">
          <div className="relative">
            <div className="w-24 h-24 bg-gradient-to-br from-red-500 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg">
              {companyData.logo ? (
                <img
                  src={URL.createObjectURL(companyData.logo)}
                  alt="Company Logo"
                  className="w-full h-full object-cover rounded-2xl"
                />
              ) : (
                <span className="text-white font-bold text-2xl">R</span>
              )}
            </div>
            {isEditing && (
              <button className="absolute -bottom-2 -right-2 w-8 h-8 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center text-white shadow-lg transition-all">
                <Camera size={16} />
              </button>
            )}
          </div>
          <div className="flex-1">
            <h4 className="text-gray-900 font-semibold mb-2">Logo hiện tại</h4>
            <p className="text-gray-600 text-sm mb-4">
              Logo sẽ được hiển thị trên tất cả tài liệu và giao diện của hệ thống
            </p>
            {isEditing && (
              <div className="flex gap-3">
                <label className="flex items-center gap-2 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg cursor-pointer transition-all">
                  <Upload size={16} />
                  <span>Tải lên mới</span>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </label>
                <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all">
                  <Trash2 size={16} />
                  <span>Xóa</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
      {/* Basic Information */}
      <div className="bg-white rounded-2xl border border-gray-200 p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-6">Thông tin cơ bản</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">Tên công ty</label>
            <div className="relative">
              <Building2
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type="text"
                value={companyData.companyName}
                onChange={e => handleInputChange('companyName', e.target.value)}
                disabled={!isEditing}
                className={`w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl text-gray-900 transition-all ${
                  isEditing
                    ? 'bg-white focus:border-red-500 focus:ring-2 focus:ring-red-100'
                    : 'bg-gray-50'
                }`}
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">Email</label>
            <div className="relative">
              <Mail
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type="email"
                value={companyData.email}
                onChange={e => handleInputChange('email', e.target.value)}
                disabled={!isEditing}
                className={`w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl text-gray-900 transition-all ${
                  isEditing
                    ? 'bg-white focus:border-red-500 focus:ring-2 focus:ring-red-100'
                    : 'bg-gray-50'
                }`}
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">Số điện thoại</label>
            <div className="relative">
              <Phone
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type="tel"
                value={companyData.phone}
                onChange={e => handleInputChange('phone', e.target.value)}
                disabled={!isEditing}
                className={`w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl text-gray-900 transition-all ${
                  isEditing
                    ? 'bg-white focus:border-red-500 focus:ring-2 focus:ring-red-100'
                    : 'bg-gray-50'
                }`}
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">Website</label>
            <div className="relative">
              <Globe
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type="url"
                value={companyData.website}
                onChange={e => handleInputChange('website', e.target.value)}
                disabled={!isEditing}
                className={`w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl text-gray-900 transition-all ${
                  isEditing
                    ? 'bg-white focus:border-red-500 focus:ring-2 focus:ring-red-100'
                    : 'bg-gray-50'
                }`}
              />
            </div>
          </div>
        </div>
        <div className="mt-6">
          <label className="block text-sm font-semibold text-gray-700 mb-3">Địa chỉ</label>
          <div className="relative">
            <MapPin className="absolute left-4 top-4 text-gray-400" size={18} />
            <textarea
              value={companyData.address}
              onChange={e => handleInputChange('address', e.target.value)}
              disabled={!isEditing}
              rows={3}
              className={`w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl text-gray-900 resize-none transition-all ${
                isEditing
                  ? 'bg-white focus:border-red-500 focus:ring-2 focus:ring-red-100'
                  : 'bg-gray-50'
              }`}
            />
          </div>
        </div>
        <div className="mt-6">
          <label className="block text-sm font-semibold text-gray-700 mb-3">Mô tả công ty</label>
          <textarea
            value={companyData.description}
            onChange={e => handleInputChange('description', e.target.value)}
            disabled={!isEditing}
            rows={4}
            className={`w-full px-4 py-4 border border-gray-200 rounded-xl text-gray-900 resize-none transition-all ${
              isEditing
                ? 'bg-white focus:border-red-500 focus:ring-2 focus:ring-red-100'
                : 'bg-gray-50'
            }`}
            placeholder="Nhập mô tả về công ty..."
          />
        </div>
      </div>
      {/* Legal Information */}
      <div className="bg-white rounded-2xl border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-bold text-gray-900">Thông tin pháp lý</h3>
          <button
            onClick={() => setShowSensitiveInfo(!showSensitiveInfo)}
            className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-all"
          >
            {showSensitiveInfo ? <EyeOff size={16} /> : <Eye size={16} />}
            <span>{showSensitiveInfo ? 'Ẩn' : 'Hiện'}</span>
          </button>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">
              Giấy phép đăng ký kinh doanh
            </label>
            <div className="relative">
              <FileText
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type="text"
                value={showSensitiveInfo ? companyData.businessLicense : '••••••••••'}
                onChange={e => handleInputChange('businessLicense', e.target.value)}
                disabled={!isEditing || !showSensitiveInfo}
                className={`w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl text-gray-900 transition-all ${
                  isEditing && showSensitiveInfo
                    ? 'bg-white focus:border-red-500 focus:ring-2 focus:ring-red-100'
                    : 'bg-gray-50'
                }`}
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">Mã số thuế</label>
            <div className="relative">
              <Hash
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type="text"
                value={showSensitiveInfo ? companyData.taxCode : '••••••••••'}
                onChange={e => handleInputChange('taxCode', e.target.value)}
                disabled={!isEditing || !showSensitiveInfo}
                className={`w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl text-gray-900 transition-all ${
                  isEditing && showSensitiveInfo
                    ? 'bg-white focus:border-red-500 focus:ring-2 focus:ring-red-100'
                    : 'bg-gray-50'
                }`}
              />
            </div>
          </div>
        </div>
        {!showSensitiveInfo && (
          <div className="mt-4 bg-amber-50 border border-amber-200 rounded-xl p-4">
            <p className="text-amber-800 text-sm">
              🔒 Thông tin nhạy cảm đã được ẩn. Click Hiện để xem và chỉnh sửa.
            </p>
          </div>
        )}
      </div>
      {/* Additional Information */}
      <div className="bg-white rounded-2xl border border-gray-200 p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-6">Thông tin bổ sung</h3>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">
              Loại hình kinh doanh
            </label>
            <select
              value={companyData.businessType}
              onChange={e => handleInputChange('businessType', e.target.value)}
              disabled={!isEditing}
              className={`w-full px-4 py-4 border border-gray-200 rounded-xl text-gray-900 transition-all ${
                isEditing
                  ? 'bg-white focus:border-red-500 focus:ring-2 focus:ring-red-100'
                  : 'bg-gray-50'
              }`}
            >
              <option value="buy-sell">Mua bán</option>
              <option value="rental">Cho thuê</option>
              <option value="both">Cả hai</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">Năm thành lập</label>
            <input
              type="number"
              value={companyData.foundedYear}
              onChange={e => handleInputChange('foundedYear', e.target.value)}
              disabled={!isEditing}
              className={`w-full px-4 py-4 border border-gray-200 rounded-xl text-gray-900 transition-all ${
                isEditing
                  ? 'bg-white focus:border-red-500 focus:ring-2 focus:ring-red-100'
                  : 'bg-gray-50'
              }`}
            />
          </div>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">
              Số lượng nhân viên
            </label>
            <select
              value={companyData.employeeCount}
              onChange={e => handleInputChange('employeeCount', e.target.value)}
              disabled={!isEditing}
              className={`w-full px-4 py-4 border border-gray-200 rounded-xl text-gray-900 transition-all ${
                isEditing
                  ? 'bg-white focus:border-red-500 focus:ring-2 focus:ring-red-100'
                  : 'bg-gray-50'
              }`}
            >
              <option value="1-10">1-10 người</option>
              <option value="11-50">11-50 người</option>
              <option value="51-100">51-100 người</option>
              <option value="101-500">101-500 người</option>
              <option value="500+">Trên 500 người</option>
            </select>
          </div>
        </div>
      </div>
      {/* Save Button */}
      {isEditing && (
        <div className="flex justify-end gap-4">
          <button
            onClick={() => setIsEditing(false)}
            className="px-6 py-3 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl transition-all font-medium"
          >
            Hủy
          </button>
          <button
            onClick={handleSave}
            className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white rounded-xl font-medium shadow-lg shadow-red-500/25 transition-all"
          >
            <Save size={18} />
            Lưu thay đổi
          </button>
        </div>
      )}
    </div>
  );

  const renderSecurityTab = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-2xl border border-gray-200 p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-6">Bảo mật tài khoản</h3>
        <div className="space-y-6">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <Key size={24} className="text-green-600" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">Đổi mật khẩu</h4>
                <p className="text-gray-600 text-sm">Cập nhật mật khẩu để bảo mật tài khoản</p>
              </div>
            </div>
            <button className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-all">
              Đổi mật khẩu
            </button>
          </div>
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <Shield size={24} className="text-blue-600" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">Xác thực 2 bước</h4>
                <p className="text-gray-600 text-sm">Tăng cường bảo mật với xác thực 2 bước</p>
              </div>
            </div>
            <button className="px-4 py-2 border border-gray-200 hover:border-gray-300 text-gray-700 rounded-lg transition-all">
              Kích hoạt
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderNotificationsTab = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-2xl border border-gray-200 p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-6">Cài đặt thông báo</h3>
        <div className="space-y-4">
          {[
            { label: 'Email thông báo', description: 'Nhận thông báo qua email' },
            { label: 'Thông báo push', description: 'Thông báo trên trình duyệt' },
            { label: 'SMS', description: 'Thông báo qua tin nhắn' },
            { label: 'Báo cáo hàng tuần', description: 'Báo cáo tổng hợp hàng tuần' },
          ].map((item, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-4 bg-gray-50 rounded-xl"
            >
              <div>
                <h4 className="font-semibold text-gray-900">{item.label}</h4>
                <p className="text-gray-600 text-sm">{item.description}</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" className="sr-only peer" defaultChecked={index < 2} />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderTab = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralTab();
      case 'security':
        return renderSecurityTab();
      case 'notifications':
        return renderNotificationsTab();
      default:
        return (
          <div className="bg-white rounded-2xl border border-gray-200 p-12 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Settings size={32} className="text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Đang phát triển</h3>
            <p className="text-gray-600">Tính năng này sẽ sớm được cập nhật</p>
          </div>
        );
    }
  };

  return (
    <div className="flex-1 bg-gradient-to-br from-gray-50 to-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-100 px-8 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <button
              onClick={onBack}
              className="flex items-center gap-3 text-gray-600 hover:text-gray-900 transition-colors font-medium"
            >
              <ArrowLeft size={20} />
              <span>Quay lại</span>
            </button>
            <div className="h-6 w-px bg-gray-300"></div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Cài đặt Công ty</h1>
              <p className="text-gray-600">Quản lý thông tin và cài đặt công ty</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl border border-gray-200 transition-all">
              <Download size={16} />
              <span className="text-sm font-medium">Xuất dữ liệu</span>
            </button>
            <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl border border-gray-200 transition-all">
              <RefreshCw size={16} />
              <span className="text-sm font-medium">Đồng bộ</span>
            </button>
          </div>
        </div>
      </div>
      <div className="p-8">
        <div className="max-w-6xl mx-auto">
          {/* Tabs */}
          <div className="bg-white rounded-2xl border border-gray-200 shadow-sm mb-8">
            <div className="flex overflow-x-auto">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-3 px-6 py-4 font-medium transition-all whitespace-nowrap border-b-2 ${
                    activeTab === tab.id
                      ? 'border-red-500 text-red-600 bg-red-50'
                      : 'border-transparent text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <tab.icon size={18} />
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
          {/* Tab Content */}
          {renderTab()}
        </div>
      </div>
    </div>
  );
};

export default CompanySettingsPage;
