import apiService from '../core';

export enum Roles {
  Admin = 'Admin',
  Saler = 'Saler',
  User = 'User',
}

export interface LoginCredentials {
  keyLogin: string;
  password: string;
}

export interface RegisterRequest {
  userName?: string;
  fullName: string;
  keyRegister: string;
  // email: string;
  password: string;
  // phoneNumber: string;
  about?: string;
  birthDate?: string;
  role: Roles;
  avatarFile?: string;
}

export interface Token {
  accessToken: string;
  refreshToken: string;
}

export interface GoogleCredentialResponse {
  credential: string;
}

export interface ZaloResponse {
  data: {
    loginUrl: string;
    state: string;
  };
}

export interface VerifyOtpRequest {
  otp: string;
  verifyKey: string;
}

export interface RequestOtpRequest {
  verifyKey: string;
}

export interface OtpResponse {
  code: string;
  status: boolean;
  message: string;
  data?: string;
}

export interface LoginResponse {
  code: string;
  status: boolean;
  message?: string;
  data: Token;
}

export interface RegisterResponse {
  code: string;
  status: true;
  message: string;
  data?: [];
}

export interface ForgotPasswordRequest {
  email: string;
}
export interface ForgotPasswordResponse {
  code: string;
  status: boolean;
  message: string;
  data?: string;
}

export interface RenewPasswordRequest {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

export interface RenewPasswordResponse {
  code: string;
  status: boolean;
  message: string;
  data?: string;
}

export const fetchAuth = {
  // Login with credentials
  login: async (credentials: LoginCredentials): Promise<LoginResponse> => {
    const response = await apiService.post<LoginResponse, LoginCredentials>(
      '/api/auth/login',
      credentials
    );
    return response.data;
  },

  // Register new user
  register: async (data: RegisterRequest): Promise<RegisterResponse> => {
    const response = await apiService.post<RegisterResponse, RegisterRequest>(
      '/api/auth/register',
      data
    );
    return response.data;
  },

  // Verify OTP
  verifyOtp: async (data: VerifyOtpRequest): Promise<OtpResponse> => {
    const response = await apiService.post<OtpResponse, VerifyOtpRequest>(
      '/api/auth/verify-otp',
      data
    );
    return response.data;
  },

  // Request OTP
  requestOtp: async (data: RequestOtpRequest): Promise<OtpResponse> => {
    const response = await apiService.post<OtpResponse, RequestOtpRequest>(
      '/api/auth/request-otp',
      data
    );
    return response.data;
  },

  // Logout - no API call, just return resolved promise
  logout: async (): Promise<void> => {
    return Promise.resolve();
  },

  //   // Refresh token
  //   refreshToken: async (): Promise<AuthResponse> => {
  //     const response = await apiService.post<AuthResponse>('/auth/refresh-token', {});
  //     return response.data;
  //   },

  // forgot password request
  requestForgotPassword: async (data: ForgotPasswordRequest): Promise<ForgotPasswordResponse> => {
    const response = await apiService.post<ForgotPasswordResponse, ForgotPasswordRequest>(
      '/api/auth/forgot-password',
      data
    );
    return response.data;
  },

  // Renew password with token
  renewPassword: async (data: RenewPasswordRequest): Promise<RenewPasswordResponse> => {
    const response = await apiService.put<RenewPasswordResponse, RenewPasswordRequest>(
      '/api/auth/renew-password',
      data
    );
    return response.data;
  },

  googleLogin: async (idToken: string): Promise<LoginResponse> => {
    const response = await apiService.post<LoginResponse>('/api/auth/google-login', { idToken });
    return response.data;
  },

  zaloLogin: async (accessToken: string): Promise<LoginResponse> => {
    const response = await apiService.post<LoginResponse>('/api/auth/zalo-login', { accessToken });
    return response.data;
  },

  getZaloLoginUrl: async (): Promise<{ loginUrl: string; state: string }> => {
    const response = await apiService.get<ZaloResponse>('/api/auth/zalo-login-url');
    return response.data.data;
  },

  zaloCallback: async (code: string, state: string): Promise<LoginResponse> => {
    const response = await apiService.get<LoginResponse>(
      `/api/auth/zalo-callback?code=${code}&state=${state}`
    );
    return response.data;
  },

  facebookLogin: async (accessToken: string): Promise<LoginResponse> => {
    const response = await apiService.post<LoginResponse>('/api/auth/facebook-login', {
      accessToken,
    });
    return response.data;
  },
};

export default fetchAuth;
