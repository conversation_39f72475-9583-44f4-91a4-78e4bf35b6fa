'use client';

import { useState, useRef } from 'react';
import { CalendarDays, Filter } from 'lucide-react';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  type ViewedTimeFilter,
  VIEWED_TIME_FILTER_OPTIONS,
  getFilterCounts,
  getViewedDates,
  isDateViewable,
} from '@/utils/dates/filterViewedTime';
import { RecentlyViewedPropertyWithTime } from '@/hooks/useRecentlyViewed';

interface ViewedTimeFilterProps {
  properties: RecentlyViewedPropertyWithTime[];
  selectedFilter: ViewedTimeFilter;
  selectedDate?: Date;
  onFilterChange: (filter: ViewedTimeFilter, date?: Date) => void;
}

export function ViewedTimeFilter({
  properties,
  selectedFilter,
  selectedDate,
  onFilterChange,
}: ViewedTimeFilterProps) {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const filterCounts = getFilterCounts(properties);
  const viewedDates = getViewedDates(properties);
  const calendarTriggerRef = useRef<HTMLButtonElement>(null);

  const handleFilterSelect = (value: ViewedTimeFilter) => {
    if (value === 'custom') {
      // Nếu chọn "Chọn ngày", hiển thị calendar
      // Delay để Select có thể đóng trước khi mở Calendar
      setTimeout(() => {
        setIsCalendarOpen(true);
      }, 200);
    } else {
      onFilterChange(value);
    }
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date && isDateViewable(date, viewedDates)) {
      onFilterChange('custom', date);
      setIsCalendarOpen(false);
    }
  };

  const isDateDisabled = (date: Date) => {
    return !isDateViewable(date, viewedDates);
  };

  const getFilterLabel = (filter: ViewedTimeFilter) => {
    const option = VIEWED_TIME_FILTER_OPTIONS.find(opt => opt.value === filter);
    if (filter === 'custom' && selectedDate) {
      return format(selectedDate, 'dd/MM/yyyy', { locale: vi });
    }
    return option?.label || 'Tất cả';
  };

  return (
    <div className="flex items-center gap-2">
      <div className="flex items-center gap-2">
        <Filter className="w-4 h-4 text-gray-500" />
        <span className="text-sm font-medium text-gray-700">Lọc theo:</span>
      </div>

      <Select value={selectedFilter} onValueChange={handleFilterSelect}>
        <SelectTrigger className="w-48">
          <SelectValue>
            <div className="flex items-center justify-between w-full">
              <span>{getFilterLabel(selectedFilter)}</span>
              {selectedFilter !== 'all' && (
                <span className="text-xs text-gray-500 ml-2">
                  (
                  {selectedFilter === 'custom' && selectedDate
                    ? properties.filter(item => {
                        const viewedDate = new Date(item.viewedAt);
                        const targetStart = new Date(
                          selectedDate.getFullYear(),
                          selectedDate.getMonth(),
                          selectedDate.getDate()
                        );
                        const viewedStart = new Date(
                          viewedDate.getFullYear(),
                          viewedDate.getMonth(),
                          viewedDate.getDate()
                        );
                        return viewedStart.getTime() === targetStart.getTime();
                      }).length
                    : filterCounts[selectedFilter]}
                  )
                </span>
              )}
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {VIEWED_TIME_FILTER_OPTIONS.map(option => (
            <SelectItem key={option.value} value={option.value}>
              <div className="flex items-center justify-between w-full">
                <span>{option.label}</span>
                {option.value !== 'custom' && (
                  <span className="text-xs text-gray-500 ml-2">({filterCounts[option.value]})</span>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Calendar Popover for custom date */}
      <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
        <PopoverTrigger asChild>
          <Button
            ref={calendarTriggerRef}
            variant="outline"
            className="opacity-0 absolute -z-10 pointer-events-none w-0 h-0"
            aria-hidden="true"
          >
            <CalendarDays className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <div className="p-3">
            <div className="text-sm text-gray-600 mb-2">
              Chỉ có thể chọn những ngày đã xem property
            </div>
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={handleDateSelect}
              disabled={isDateDisabled}
              initialFocus
              locale={vi}
            />
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
