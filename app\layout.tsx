import type { Metadata, Viewport } from 'next';
import './globals.css';
import { Inter } from 'next/font/google';
import { Providers } from '@/lib/providers';
import { Toaster } from '@/components/ui/sonner';
import { NavigationProgressProvider } from '@/components/providers/navigationProgressProvider';
import { RecentlyViewedSyncProvider } from '@/components/providers/recentlyViewedSyncProvider';
// import { I18nextProvider } from '@/components/i18next-provider';
// import { I18nextProvider } from '@/components/i18next-provider';
// import { ChatWidget } from '@/components/chatWidget';
// import { ChatWidget } from '@/components/chatWidget';
import { Analytics } from '@vercel/analytics/next';
import { NotificationProvider } from '@/components/providers/notificationProvider';
import { AppInitializer } from '@/components/providers/appInitializer';
// import { AuthDebugInfo } from '@/components/common/AuthDebugInfo';
import { AuthSyncProvider } from '@/components/providers/authSyncProvider';

const inter = Inter({ subsets: ['latin'] });

export async function generateMetadata(): Promise<Metadata> {
  return {
    metadataBase: new URL('https://www.revoland.vn'),
    title: 'Revoland - Nền Tảng Công Nghệ Bất Động Sản Toàn Diện',
    description:
      'Revoland - Giải pháp bất động sản toàn diện, đáng tin cậy. Chuyên cung cấp dịch vụ mua bán, cho thuê nhà đất, biệt thự, căn hộ và đất nền với đội ngũ chuyên gia uy tín, tận tâm.',
    applicationName: 'Revoland',
    authors: [{ name: 'Revoland', url: 'https://www.revoland.vn' }],
    keywords: [
      'Revoland',
      'bất động sản',
      'cho thuê nhà đất',
      'tư vấn bất động sản',
      'đầu tư bất động sản',
      'mua bán bất động sản',
      'giải pháp bất động sản',
      'Revoland',
      'Revoland Solutions',
    ],
    creator: 'Revoland Team',
    publisher: 'Revoland',
    openGraph: {
      type: 'website',
      locale: 'vi_VN',
      url: 'https://www.revoland.vn',
      siteName: 'Revoland',
      title: 'Revoland - Giải Pháp Bất Động Sản Toàn Diện',
      description:
        'Revoland mang đến các giải pháp bất động sản tối ưu, đáp ứng mọi nhu cầu từ mua bán, cho thuê đến tư vấn đầu tư. Trải nghiệm dịch vụ chuyên nghiệp và đáng tin cậy tại Revoland.',
      images: [
        {
          url: 'https://www.revoland.vn/banner_revoland.jpg',
          width: 1200,
          height: 630,
          alt: 'Revoland Real Estate Banner',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      site: '@Revoland_official',
      title: 'Revoland - Giải Pháp Bất Động Sản Toàn Diện',
      description:
        'Khám phá giải pháp bất động sản toàn diện tại Revoland. Chúng tôi cung cấp dịch vụ mua bán, cho thuê, và tư vấn đầu tư bất động sản với sự chuyên nghiệp và tận tâm.',
      images: [
        {
          url: 'https://www.revoland.vn/banner_revoland.jpg',
          alt: 'Revoland Real Estate Banner',
        },
      ],
    },
    robots: {
      index: true,
      follow: true,
    },
    icons: {
      icon: '/LOGO_RV_red-01-01.png',
      apple: '/LOGO_RV_red-01-01.png',
    },
    verification: {
      google: 'wuPoPe7TpEauAX0v9AokNhzGWkr9y9HmjeQKedjcOVA',
      other: {
        'zalo-platform-site-verification': 'PVxl8wECVIyYklSAuf1c4cIMzoo8u25nCJSn',
      },
    },
  };
}

export const viewport: Viewport = {
  themeColor: '#1E293B',
  colorScheme: 'light',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="vi" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <NotificationProvider enabled={true}>
            <NavigationProgressProvider>
              <AppInitializer>
                <AuthSyncProvider>
                  <RecentlyViewedSyncProvider>
                    <main>
                      {children} <Analytics />
                    </main>
                    {/* <AuthDebugInfo /> */}
                  </RecentlyViewedSyncProvider>
                </AuthSyncProvider>
              </AppInitializer>
              <Toaster richColors closeButton={true} expand={true} />
            </NavigationProgressProvider>
          </NotificationProvider>
        </Providers>
      </body>
    </html>
  );
}
