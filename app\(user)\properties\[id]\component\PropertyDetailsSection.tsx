'use client';

import { Property, TransactionType, ApartmentOrientation } from '@/lib/api/services/fetchProperty';
import {
  BedDouble,
  Bath,
  Home,
  Calendar,
  MapPin,
  CheckCircle,
  Car,
  ArrowUpDown,
  Waves,
  Dumbbell,
  Shield,
  Wind,
  Building,
  Trees,
  PlayCircle,
  Power,
  Building2,
  HomeIcon,
  Compass,
  DollarSign,
  Heart,
  Share2,
  Scan,
  ChefHat,
  Bookmark,
  MessageCircle,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { TooltipProvider } from '@/components/ui/tooltip';
import PropertyMap from './PropertyMap';
import { cn } from '@/lib/utils';
import { useState, useEffect, useContext, useRef } from 'react';
import AppointmentDialog from './AppointmentDialog';
import Image from 'next/image';
import { useFavoriteProperty } from '@/hooks/useFavoriteProperty';
import { useChat } from '@/hooks/useChat';
import { signalRService } from '@/lib/realtime/signalR';
import ContactSideBar from './ContactSideBar';
import RentedCalculationForm from './RentedCalculationForm';
import PropertyCostCalculator from './PropertyCostCalculator';
import { TruncatedText } from '@/components/ui/truncated-text';
import MortgageCalculator from './MortgageCalculator';
import { ChatWidgetVisibilityContext } from '@/lib/providers/chatProvider';
import LoanDialog from './LoanDialog';
import AddToCollectionModal from '@/components/collection/AddToCollectionModal';
import LoginRequiredModal from '@/components/collection/LoginRequiredModal';
import { useAuthStore } from '@/lib/store/authStore';
import { useCollections } from '@/hooks/useCollections';

interface PropertyDetailsSectionProps {
  property: Property;
}

export default function PropertyDetailsSection({ property }: PropertyDetailsSectionProps) {
  const { addFavorite, removeFavorite, isAddingFavorite, isRemovingFavorite } =
    useFavoriteProperty();
  const { isPropertyInCollections, removeFromAllCollections, isRemovingFromAllCollections } =
    useCollections();
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  const [showCalculationTool, setShowCalculationTool] = useState(false);
  const { openChat } = useContext(ChatWidgetVisibilityContext);
  const { askRevolandWithPropertyCard, sendMessage, refetchConversation } = useChat();
  const [showAppointmentDialog, setShowAppointmentDialog] = useState(false);
  const [showLoanDialog, setShowLoanDialog] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showFocusedImage, setShowFocusedImage] = useState(false);
  const [showAddToCollectionModal, setShowAddToCollectionModal] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [askInput, setAskInput] = useState('');
  const askInputRef = useRef<HTMLInputElement>(null);

  // Initialize favorite state from property
  const [isFavorite, setIsFavorite] = useState(property.isFavorite || false);

  // Update local state when property.isFavorite changes
  useEffect(() => {
    setIsFavorite(property.isFavorite || false);
  }, [property.isFavorite]);

  // Check if property is in any collection for bookmark styling
  const isInCollection = isPropertyInCollections(property.id);

  console.log(currentImageIndex);
  console.log(showFocusedImage);

  const handleFavoriteClick = () => {
    if (isFavorite) {
      removeFavorite(property.id);
      setIsFavorite(false);
    } else {
      addFavorite({
        propertyId: property.id,
        savedAt: new Date().toISOString(),
      });
      setIsFavorite(true);
    }
  };

  const handleCollectionClick = () => {
    if (!isAuthenticated) {
      setShowLoginModal(true);
      return;
    }

    // If property is already in collection, remove from all collections
    if (isInCollection) {
      removeFromAllCollections(property.id);
    } else {
      // If not in collection, show add to collection modal
      setShowAddToCollectionModal(true);
    }
  };

  // New handler for sending propertyCard and then user message
  const handleAskRevoland = async (message?: string) => {
    const conversationId = await askRevolandWithPropertyCard(property.saler.id, property);
    await new Promise(res => setTimeout(res, 300)); // thử tăng delay
    if (message && message.trim() && conversationId) {
      openChat();
      try {
        await sendMessage({
          conversationId,
          recipientId: property.saler.id,
          content: message,
          direction: 'inbound',
        });
        await signalRService.connect();
        await signalRService.joinConversation(conversationId, property.saler.id);
        for (let i = 0; i < 5; i++) {
          await new Promise(res => setTimeout(res, 300));
          await refetchConversation();
          if (conversationId) {
            await signalRService.loadMoreMessages(conversationId, 1, 20);
          }
        }
        console.log('Text message sent!');
      } catch (err) {
        console.error('Error sending text message:', err);
      }
    }
    setAskInput('');
  };
  // function formatPriceShort(value: number): string {
  //   if (value >= 1_000_000_000) {
  //     return (value / 1_000_000_000).toFixed(1).replace(/\.0$/, '') + ' tỷ';
  //   }
  //   if (value >= 1_000_000) {
  //     return (value / 1_000_000).toFixed(1).replace(/\.0$/, '') + ' triệu';
  //   }
  //   if (value >= 1_000) {
  //     return (value / 1_000).toFixed(1).replace(/\.0$/, '') + ' nghìn';
  //   }
  //   return value.toString();
  // }

  return (
    <div className="flex flex-col lg:flex-row gap-8">
      {/* Main Content - 2/3 width */}
      <div className="lg:w-2/3">
        {/* Property Header */}
        <div className="mb-4 md:mb-8">
          <div className="flex flex-col md:flex-row md:items-start justify-between gap-2 md:gap-0 mb-2">
            <div className="flex-1 min-w-0 order-1 md:order-1">
              <TruncatedText
                text={property.title}
                maxWords={7} // Show first 50 words
                as="h1"
                className="text-2xl md:text-4xl font-medium"
                buttonText={{
                  show: 'Xem thêm',
                  hide: 'Thu gọn',
                }}
              />
            </div>
            <div className="flex gap-2 order-2 md:order-2 self-start">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1 md:gap-2 text-xs md:text-sm h-8 md:h-9 px-2 md:px-4"
                onClick={() => {
                  // Add share functionality here
                  if (navigator.share) {
                    navigator.share({
                      title: property.title,
                      url: window.location.href,
                    });
                  }
                }}
              >
                <Share2 className="h-3 w-3 md:h-4 md:w-4" />
                {/* <span className="hidden sm:inline">Chia sẻ</span> */}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className={cn(
                  'flex items-center gap-1 md:gap-2 text-xs md:text-sm h-8 md:h-9 px-2 md:px-4 transition-colors',
                  isInCollection &&
                    'text-yellow-600 border-yellow-600 hover:text-yellow-700 hover:border-yellow-700',
                  isRemovingFromAllCollections && 'opacity-50 cursor-not-allowed'
                )}
                onClick={handleCollectionClick}
                disabled={isRemovingFromAllCollections}
              >
                <Bookmark
                  className={cn('h-3 w-3 md:h-4 md:w-4', isInCollection && 'fill-yellow-600')}
                />
                {/* <span className="hidden sm:inline">Bộ sưu tập</span> */}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className={cn(
                  'flex items-center gap-1 md:gap-2 text-xs md:text-sm h-8 md:h-9 px-2 md:px-4 transition-colors',
                  isFavorite &&
                    'text-red-600 border-red-600 hover:text-red-700 hover:border-red-700',
                  (isAddingFavorite || isRemovingFavorite) && 'opacity-50 cursor-not-allowed'
                )}
                onClick={handleFavoriteClick}
                disabled={isAddingFavorite || isRemovingFavorite}
              >
                <Heart className={cn('h-3 w-3 md:h-4 md:w-4', isFavorite && 'fill-current')} />
                {/* <span className="hidden sm:inline">Lưu</span> */}
              </Button>
              {/* <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1 md:gap-2 text-xs md:text-sm h-8 md:h-9 px-2 md:px-4"
                onClick={() => {
                  // Add warning functionality here
                }}
              >
                <AlertTriangle className="h-3 w-3 md:h-4 md:w-4" />
                <span className="hidden sm:inline">Lưu</span>
              </Button> */}
            </div>
          </div>
          <div className="flex items-center text-xs md:text-base text-muted-foreground">
            <MapPin className="size-3 md:size-4 mr-1" />
            <span>
              {property.location.address}, {property.location.district}, {property.location.city}
            </span>
          </div>
          {property.code && (
            <div className="flex items-center gap-2 mt-2">
              <span className="text-xs text-muted-foreground">
                Mã bất động sản: {property.code}
              </span>
            </div>
          )}
        </div>

        <Separator className="my-4 md:my-6" />

        {/* Property Specs */}
        <div className="flex flex-wrap gap-2 md:gap-4 mb-4 md:mb-8">
          <PropertySpec
            icon={<BedDouble className="size-4" />}
            value={`${property.propertyDetails.bedrooms} phòng ngủ`}
            label="Bedrooms"
          />
          <PropertySpec
            icon={<Bath className="size-4" />}
            value={`${property.propertyDetails.bathrooms} phòng tắm`}
            label="Bathrooms"
          />
          <PropertySpec
            icon={<Home className="size-4" />}
            value={`${property.propertyDetails.livingRooms} phòng khách`}
            label="Living Rooms"
          />
          <PropertySpec
            icon={<ChefHat className="size-4" />}
            value={`${property.propertyDetails.kitchens} phòng bếp`}
            label="Kitchens"
          />
          <PropertySpec
            icon={<Building2 className="size-4" />}
            value={`${property.propertyDetails.numberOfFloors} tầng`}
            label="Floors"
          />
          <PropertySpec
            icon={<Scan className="size-4" />}
            value={`${property.propertyDetails.buildingArea} m²`}
            label="Land Area"
          />
          <PropertySpec
            icon={<Calendar className="size-4" />}
            value={`${property.yearBuilt}`}
            label="Year Built"
          />
        </div>

        {/* Description Section */}
        <Card id="description" className="mb-4 md:mb-8">
          <CardHeader className="max-md:p-4">
            <CardTitle className="flex items-center gap-2 text-lg md:text-xl">
              <Home className="size-4 md:size-5" />
              Mô tả
            </CardTitle>
            <CardDescription>Thông tin chi tiết về bất động sản</CardDescription>
          </CardHeader>
          {/* <Separator className='max-md:hidden' /> */}
          <CardContent className="max-md:p-4">
            <p className="text-foreground font-sans font-light leading-relaxed text-sm md:text-base">
              {property.description}
            </p>
          </CardContent>
        </Card>

        {/* Features & Amenities */}
        <Card id="amenities" className="mb-4 md:mb-8">
          <CardHeader className="max-md:p-4">
            <CardTitle className="flex items-center gap-2 text-lg md:text-xl">
              <CheckCircle className="size-4 md:size-5" />
              Tiện ích & Tiện nghi
            </CardTitle>
            <CardDescription>Danh sách các tiện ích và tiện nghi có sẵn</CardDescription>
          </CardHeader>
          <CardContent className="max-md:p-4">
            <div className="grid grid-cols-2 sm:grid-cols-2 gap-0">
              {Object.entries(property.amenities).map(
                ([amenity, isAvailable], index) =>
                  isAvailable && (
                    <div key={index} className="relative">
                      <div className="flex items-center gap-3 py-3">
                        {amenity === 'parking' && (
                          <Car className="size-4 md:size-5  flex-shrink-0" />
                        )}
                        {amenity === 'elevator' && (
                          <ArrowUpDown className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'swimmingPool' && (
                          <Waves className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'gym' && (
                          <Dumbbell className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'securitySystem' && (
                          <Shield className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'airConditioning' && (
                          <Wind className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'balcony' && (
                          <Building className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'garden' && (
                          <Trees className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'playground' && (
                          <PlayCircle className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'backupGenerator' && (
                          <Power className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        <span className="font-medium capitalize text-sm md:text-base">
                          {amenity === 'parking' && 'Bãi đỗ xe'}
                          {amenity === 'elevator' && 'Thang máy'}
                          {amenity === 'swimmingPool' && 'Hồ bơi'}
                          {amenity === 'gym' && 'Phòng gym'}
                          {amenity === 'securitySystem' && 'Hệ thống an ninh'}
                          {amenity === 'airConditioning' && 'Điều hòa'}
                          {amenity === 'balcony' && 'Ban công'}
                          {amenity === 'garden' && 'Vườn'}
                          {amenity === 'playground' && 'Sân chơi'}
                          {amenity === 'backupGenerator' && 'Máy phát điện'}
                        </span>
                      </div>
                      {/* Add separator if not the last item */}
                      {/* {index < array.length - 1 && (
                        <Separator className="absolute bottom-0 left-0 right-0" />
                      )} */}
                    </div>
                  )
              )}
            </div>
          </CardContent>
        </Card>
        {/* Ask/Chat Box Section */}
        <div
          id="chat"
          className="propertyChat md:border-2 border border-zinc-200 rounded-md md:rounded-xl bg-background/50 mb-4 md:mb-8 p-4 md:p-6"
        >
          <div className="mb-2 flex items-center gap-2">
            <h2 className="text-lg md:text-xl font-semibold">Đặt câu hỏi cùng Revoland</h2>
            <span className="bg-red-500 text-white text-xs font-bold rounded-full px-2 py-0.5 ml-2">
              Mới!
            </span>
          </div>
          <div className="flex items-start gap-2 mb-2">
            <div className=" rounded-full  bg-gray-100 flex items-center justify-center">
              <Image
                src="/logo_revoland_red.png"
                alt="Revoland"
                width={52}
                height={52}
                className="p-1"
              />
            </div>
            <div>
              <div className="bg-zinc-100 rounded-lg px-3 py-1 mb-1 text-sm w-fit">
                Xin chào
                <span role="img" aria-label="wave">
                  👋
                </span>
                Chào mừng bạn đến với Revoland – Nền tảng bất động sản thông minh!
              </div>
              <div className="bg-zinc-100 rounded-lg px-3 py-1 text-sm w-[90%]">
                Chúng tôi luôn sẵn sàng hỗ trợ bạn tìm kiếm, mua bán hoặc đầu tư bất động sản một
                cách nhanh chóng và hiệu quả. Hãy cho chúng tôi biết bạn đang quan tâm điều gì –
                chúng tôi sẽ hỗ trợ ngay!
              </div>
            </div>
          </div>
          <div className="mb-4 mt-10 flex flex-col gap-10 border-2 border-zinc-200 rounded-md p-2">
            <input
              ref={askInputRef}
              className="w-full  px-3 py-2 text-sm focus:outline-none  "
              placeholder="Nhập tin nhắn của bạn..."
              value={askInput}
              onChange={e => setAskInput(e.target.value)}
              onKeyDown={e => {
                if (e.key === 'Enter' && askInput.trim()) {
                  handleAskRevoland(askInput);
                }
              }}
            />

            <div className="flex flex-col sm:flex-row gap-2 mb-2">
              <button
                className="flex-1 border border-zinc-300 rounded-md px-3 py-2 text-sm font-semibold text-red-600 hover:bg-red-50 transition"
                onClick={() => handleAskRevoland('Tôi muốn biết thêm chi tiết')}
              >
                Tôi muốn biết thêm chi tiết
              </button>
              <button
                className="flex-1 border border-zinc-300 rounded-md px-3 py-2 text-sm font-semibold text-red-600 hover:bg-red-50 transition"
                onClick={() => handleAskRevoland('Tôi muốn mua')}
              >
                Tôi muốn mua
              </button>
              <button
                className="flex-1 border border-zinc-300 rounded-md px-3 py-2 text-sm font-semibold text-red-600 hover:bg-red-50 transition"
                onClick={() => handleAskRevoland('Căn này còn trống không?')}
              >
                Căn này còn trống không?
              </button>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              className="bg-red-600 text-white rounded-md px-4 py-2 font-semibold flex items-center gap-2"
              onClick={() => askInput.trim() && handleAskRevoland(askInput)}
            >
              <MessageCircle className="w-4 h-4" />
              Gửi tin nhắn
            </button>
            <span className="text-xs text-muted-foreground">
              Đường dây nóng:{' '}
              <a href="tel:0968070478" className="text-blue-600 font-semibold">
                0968070478
              </a>
            </span>
          </div>
        </div>

        {/* Location Details */}
        <Card id="location" className="mb-4 md:mb-8">
          <CardHeader className="max-md:p-4">
            <CardTitle className="flex items-center gap-2 text-lg md:text-xl">
              <MapPin className="size-4 md:size-5" />
              Địa chỉ
            </CardTitle>
            <CardDescription>Thông tin địa chỉ và vị trí bất động sản</CardDescription>
          </CardHeader>
          <CardContent className="max-md:p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-xs md:text-sm text-muted-foreground">Địa chỉ</p>
                <p className="font-medium">{property.location.address}</p>
              </div>
              <div>
                <p className="text-xs md:text-sm text-muted-foreground">Thành phố</p>
                <p className="font-medium">{property.location.city}</p>
              </div>
              <div>
                <p className="text-xs md:text-sm text-muted-foreground">Quận</p>
                <p className="font-medium">{property.location.district}</p>
              </div>
              <div>
                <p className="text-xs md:text-sm text-muted-foreground">Phường</p>
                <p className="font-medium">{property.location.ward}</p>
              </div>
            </div>

            {/* Property Map */}
            <div className="mt-4 md:mt-6">
              <PropertyMap
                latitude={property.location.latitude}
                longitude={property.location.longitude}
                address={property.location.address}
                title={property.title}
                imageUrl={property.imageUrls[0] || '/placeholder-property.jpg'}
                size={500}
              />
            </div>
          </CardContent>
        </Card>

        {/* Floor Plans Section */}
        {property?.floorPlanUrls.length > 0 && (
          <Card id="floor-plans" className="mb-4 md:mb-8">
            <CardHeader className="max-md:p-4">
              <CardTitle className="flex items-center gap-2 text-lg md:text-xl">
                <Building2 className="size-4 md:size-5" />
                Bản vẽ tòa nhà
              </CardTitle>
              <CardDescription>Bản vẽ và sơ đồ tòa nhà</CardDescription>
            </CardHeader>
            <CardContent className="max-md:p-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {property?.floorPlanUrls.map((floorPlan, index) => (
                  <div
                    key={index}
                    className="relative aspect-[4/3] rounded-lg overflow-hidden border-2 border-zinc-200"
                  >
                    <Image
                      src={floorPlan}
                      alt={`${property.title} - Floor Plan ${index + 1}`}
                      fill
                      className="object-cover hover:opacity-95 transition-opacity cursor-pointer"
                      onClick={() => {
                        setCurrentImageIndex(index);
                        setShowFocusedImage(true);
                      }}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Videos Section */}
        {property?.video?.videoUrl && (
          <Card id="videos" className="mb-4 md:mb-8">
            <CardHeader className="max-md:p-4">
              <CardTitle className="flex items-center gap-2 text-lg md:text-xl">
                <PlayCircle className="size-4 md:size-5" />
                Video giới thiệu
              </CardTitle>
              <CardDescription>Video giới thiệu về bất động sản</CardDescription>
            </CardHeader>
            <CardContent className="max-md:p-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="relative aspect-video rounded-lg overflow-hidden bg-black">
                  {property?.video?.videoUrl && (
                    <video
                      className="w-full h-full object-contain"
                      controls
                      playsInline
                      preload="metadata"
                    >
                      <source src={property.video.videoUrl} type="video/mp4" />
                      Your browser does not support the video tag.
                    </video>
                  )}
                </div>
                <div className="mt-4">
                  <h3 className="text-base md:text-lg font-medium mb-2">
                    {property?.video?.title}
                  </h3>
                  <p className="text-muted-foreground text-xs md:text-sm">
                    {property?.video?.description}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
        <TooltipProvider>
          {property.transactionType === TransactionType.FOR_RENT &&
            property.priceDetails.rentalPrice && (
              <PropertyCostCalculator propertyData={property.priceDetails} />
            )}
        </TooltipProvider>
        {property.transactionType === TransactionType.FOR_SALE &&
          property.priceDetails.salePrice && (
            <MortgageCalculator
              initialPropertyValue={property.priceDetails.salePrice || 0}
              onDialog={false}
            />
          )}
        {/* Property Details */}
        <Card id="details" className="mb-4 md:mb-8">
          <CardHeader className="max-md:p-4">
            <CardTitle className="flex items-center gap-2 text-lg md:text-xl">
              <Home className="size-4 md:size-5" />
              Chi tiết
            </CardTitle>
            <CardDescription>Thông tin chi tiết về bất động sản</CardDescription>
          </CardHeader>
          <CardContent className="max-md:p-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              <PropertyDetailCard
                icon={<Building2 className="size-4 md:size-5" />}
                label="Loại bất động sản"
                value={
                  (property.type === 'Apartment' && 'Căn hộ') ||
                  (property.type === 'Villa' && 'Biệt thự') ||
                  (property.type === 'ShopHouse' && 'Nhà phố') ||
                  (property.type === 'LandPlot' && 'Đất nền') ||
                  property.type
                }
              />
              <PropertyDetailCard
                icon={<CheckCircle className="size-4 md:size-5" />}
                label="Trạng thái"
                value={
                  (property.status === 'Available' && 'Có sẵn') ||
                  (property.status === 'Pending' && 'Đang xử lý') ||
                  (property.status === 'Sold' && 'Đã bán') ||
                  (property.status === 'Rented' && 'Đã cho thuê') ||
                  property.status
                }
                valueColor={
                  (property.status === 'Available' && 'text-green-600') ||
                  (property.status === 'Pending' && 'text-yellow-600') ||
                  (property.status === 'Sold' && 'text-red-600') ||
                  (property.status === 'Rented' && 'text-blue-600') ||
                  'text-foreground'
                }
              />
              <PropertyDetailCard
                icon={<DollarSign className="size-4 md:size-5" />}
                label="Loại giao dịch"
                value={
                  (property.transactionType === TransactionType.FOR_SALE && 'Bán') ||
                  (property.transactionType === TransactionType.FOR_RENT && 'Cho thuê') ||
                  property.transactionType
                }
              />
              <PropertyDetailCard
                icon={<Home className="size-4 md:size-5" />}
                label="Mã bất động sản"
                value={property.code}
              />
              <PropertyDetailCard
                icon={<Home className="size-4 md:size-5" />}
                label="Phòng khách"
                value={property.propertyDetails.livingRooms}
              />
              <PropertyDetailCard
                icon={<ChefHat className="size-4 md:size-5" />}
                label="Phòng bếp"
                value={property.propertyDetails.kitchens}
              />
              <PropertyDetailCard
                icon={<Scan className="size-4 md:size-5" />}
                label="Diện tích đất"
                value={`${property.propertyDetails.landArea} m²`}
              />
              <PropertyDetailCard
                icon={<ArrowUpDown className="size-4 md:size-5" />}
                label="Chiều rộng đất"
                value={`${property.propertyDetails.landWidth} m`}
              />
              <PropertyDetailCard
                icon={<ArrowUpDown className="size-4 md:size-5" />}
                label="Chiều dài đất"
                value={`${property.propertyDetails.landLength} m`}
              />
              <PropertyDetailCard
                icon={<Building2 className="size-4 md:size-5" />}
                label="Số tầng"
                value={property.propertyDetails.numberOfFloors}
              />
              <PropertyDetailCard
                icon={<Building className="size-4 md:size-5" />}
                label="Tầng hầm"
                value={property.propertyDetails.hasBasement ? 'Có' : 'Không'}
              />
              <PropertyDetailCard
                icon={<HomeIcon className="size-4 md:size-5" />}
                label="Trang bị nội thất"
                value={property.propertyDetails.furnished ? 'Có' : 'Không'}
              />
              <PropertyDetailCard
                icon={<Compass className="size-4 md:size-5" />}
                label="Hướng"
                value={
                  (property.propertyDetails.apartmentOrientation === ApartmentOrientation.NORTH &&
                    'Hướng Bắc') ||
                  (property.propertyDetails.apartmentOrientation === ApartmentOrientation.SOUTH &&
                    'Hướng Nam') ||
                  (property.propertyDetails.apartmentOrientation === ApartmentOrientation.EAST &&
                    'Hướng Đông') ||
                  (property.propertyDetails.apartmentOrientation === ApartmentOrientation.WEST &&
                    'Hướng Tây') ||
                  (property.propertyDetails.apartmentOrientation ===
                    ApartmentOrientation.NORTHEAST &&
                    'Hướng Đông Bắc') ||
                  (property.propertyDetails.apartmentOrientation ===
                    ApartmentOrientation.NORTHWEST &&
                    'Hướng Tây Bắc') ||
                  (property.propertyDetails.apartmentOrientation ===
                    ApartmentOrientation.SOUTHEAST &&
                    'Hướng Đông Nam') ||
                  (property.propertyDetails.apartmentOrientation ===
                    ApartmentOrientation.SOUTHWEST &&
                    'Hướng Tây Nam') ||
                  'Chưa xác định'
                }
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sticky Sidebar - 1/3 width */}
      <ContactSideBar
        property={property}
        onShowCalculationTool={() => setShowCalculationTool(true)}
        onShowAppointment={() => setShowAppointmentDialog(true)}
        onShowLoan={() => setShowLoanDialog(true)}
        topPosition="top-32"
      />
      <AppointmentDialog
        isOpen={showAppointmentDialog}
        onClose={() => setShowAppointmentDialog(false)}
        propertyId={property.id}
        propertyName={property.title}
      />
      <RentedCalculationForm
        isOpen={showCalculationTool}
        onClose={() => setShowCalculationTool(false)}
        propertyData={property?.priceDetails}
      />
      <LoanDialog
        isOpen={showLoanDialog}
        onClose={() => setShowLoanDialog(false)}
        property={property}
      />
      <AddToCollectionModal
        isOpen={showAddToCollectionModal}
        onClose={() => setShowAddToCollectionModal(false)}
        propertyId={property.id}
        _propertyTitle={property.title}
      />
      <LoginRequiredModal isOpen={showLoginModal} onClose={() => setShowLoginModal(false)} />
    </div>
  );
}

function PropertySpec({
  icon,
  value,
  label,
}: {
  icon: React.ReactNode;
  value: React.ReactNode;
  label: string;
}) {
  console.log(label);
  return (
    <div className="flex top- flex-col items-center bg-zinc-50/50 p-2 rounded-md md:rounded-xl border md:border-2 border-zinc-200">
      <div className="flex items-center gap-2">
        {icon}
        <p className="text-foreground text-xs md:text-sm">{value}</p>
      </div>
      {/* <p className="text-muted-foreground text-sm">{label}</p> */}
    </div>
  );
}

function PropertyDetailCard({
  icon,
  label,
  value,
  valueColor = 'text-foreground',
}: {
  icon: React.ReactNode;
  label: string;
  value: React.ReactNode;
  valueColor?: string;
}) {
  return (
    <div className="flex items-center gap-3 p-3 bg-zinc-50/50 rounded-md border border-zinc-200">
      <div className="flex-shrink-0 text-muted-foreground">{icon}</div>
      <div className="flex-1 min-w-0">
        <p className="text-sm text-muted-foreground mb-1">{label}</p>
        <p className={`font-medium text-base ${valueColor}`}>{value}</p>
      </div>
    </div>
  );
}
